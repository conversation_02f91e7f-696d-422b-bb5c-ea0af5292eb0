package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"cloud.google.com/go/spanner"
	"google.golang.org/api/option"
)

func main() {
	ctx := context.Background()

	// Connect to local Spanner emulator for entity-post database
	postClient, err := spanner.NewClient(ctx, "projects/backend-core-dev/instances/bereal-local-instance/databases/entity-post",
		option.WithEndpoint("localhost:9011"),
		option.WithoutAuthentication())
	if err != nil {
		log.Fatalf("Failed to create Spanner client for entity-post: %v", err)
	}
	defer postClient.Close()

	// Create test moment and post data
	if err := createMomentAndPostData(ctx, postClient); err != nil {
		log.Fatalf("Failed to create moment and post data: %v", err)
	}

	fmt.Println("Moment and post test data created successfully!")
}

func createMomentAndPostData(ctx context.Context, client *spanner.Client) error {
	now := time.Now()
	var mutations []*spanner.Mutation

	// Create test posts for the last 30 days to simulate different streak scenarios
	for i := 0; i < 30; i++ {
		dayOffset := -i
		postDate := now.AddDate(0, 0, dayOffset)
		momentID := fmt.Sprintf("moment-%d-days-ago", i)

		// Perfect streak user - posted every day
		mutations = append(mutations, spanner.InsertOrUpdate(
			"user_posts",
			[]string{"UserID", "CreatedAt", "PostID", "PostType", "PostFormat", "MomentID", "UpdatedAt"},
			[]interface{}{
				"user-perfect-streak",
				postDate,
				fmt.Sprintf("post-perfect-%d", i),
				1, // PostType
				1, // PostFormat
				momentID,
				spanner.CommitTimestamp,
			},
		))

		// User with gaps - posted on days 0, 1, 4, 5 (missing days 2, 3)
		if i == 0 || i == 1 || i == 4 || i == 5 {
			mutations = append(mutations, spanner.InsertOrUpdate(
				"user_posts",
				[]string{"UserID", "CreatedAt", "PostID", "PostType", "PostFormat", "MomentID", "UpdatedAt"},
				[]interface{}{
					"user-with-gaps",
					postDate,
					fmt.Sprintf("post-gaps-%d", i),
					1, // PostType
					1, // PostFormat
					momentID,
					spanner.CommitTimestamp,
				},
			))
		}

		// User with many gaps - only posted on days 0, 5, 10, 15, 20, 25 (many gaps)
		if i%5 == 0 {
			mutations = append(mutations, spanner.InsertOrUpdate(
				"user_posts",
				[]string{"UserID", "CreatedAt", "PostID", "PostType", "PostFormat", "MomentID", "UpdatedAt"},
				[]interface{}{
					"user-many-gaps",
					postDate,
					fmt.Sprintf("post-many-gaps-%d", i),
					1, // PostType
					1, // PostFormat
					momentID,
					spanner.CommitTimestamp,
				},
			))
		}

		// User with old gaps - posted recently but has gaps 35-40 days ago (outside 30-day window)
		if i < 5 { // Only recent posts
			mutations = append(mutations, spanner.InsertOrUpdate(
				"user_posts",
				[]string{"UserID", "CreatedAt", "PostID", "PostType", "PostFormat", "MomentID", "UpdatedAt"},
				[]interface{}{
					"user-old-gaps",
					postDate,
					fmt.Sprintf("post-old-gaps-%d", i),
					1, // PostType
					1, // PostFormat
					momentID,
					spanner.CommitTimestamp,
				},
			))
		}
	}

	// Create some older posts for the old gaps user (35-40 days ago)
	for i := 35; i <= 40; i++ {
		dayOffset := -i
		postDate := now.AddDate(0, 0, dayOffset)
		momentID := fmt.Sprintf("moment-%d-days-ago", i)

		// Only post on some days to create gaps
		if i%2 == 0 {
			mutations = append(mutations, spanner.InsertOrUpdate(
				"user_posts",
				[]string{"UserID", "CreatedAt", "PostID", "PostType", "PostFormat", "MomentID", "UpdatedAt"},
				[]interface{}{
					"user-old-gaps",
					postDate,
					fmt.Sprintf("post-old-gaps-old-%d", i),
					1, // PostType
					1, // PostFormat
					momentID,
					spanner.CommitTimestamp,
				},
			))
		}
	}

	// Apply all mutations
	_, err := client.Apply(ctx, mutations)
	if err != nil {
		return fmt.Errorf("failed to insert post data: %w", err)
	}

	fmt.Println("✅ Created test posts for different streak scenarios")
	return nil
}
