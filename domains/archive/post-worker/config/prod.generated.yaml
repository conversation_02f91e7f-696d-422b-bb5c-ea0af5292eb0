# This file is generated by _tools/scripts/genconfig.sh; DO NOT EDIT.
backend-apps:
  configFiles:
    platform.conf.yaml: |-
      templates:
        logger: &default-logger
          level: warn
          mode: json
      
        # microservices is not injected in application config per se, it acts as a placeholder
        # for gathering services' adresses. Anchoring (&blabla) is heavily used to inject
        # the values everywhere it's needed.
        microservices:
          # some doc can be found here https://pkg.go.dev/google.golang.org/grpc/keepalive#ClientParameters
          keepalive: &grpc-default-keepalive ~
          # path in the container, you can find the workdir declared here: _tools/docker/monolith/Dockerfile
          protoBasePath: &proto-base-path "../../../backend-proto/"
          maxConnectionAgeMs: &grpc-max-connection-age-ms 30_000 # 30s
          maxConnectionAgeGraceMs: &grpc-max-connection-age-grace-ms 10_000 # 10s
          services:
            entityUser:
              url: &entity-user-url entity-user-grpc-api-headless.entity.svc.cluster.local.:8080
              flags: &entity-user-flags
                enableUpdations: true
                enableInsertions: true
                enableDeletions: true
              maxBatchSize: &entity-user-maxBatchSize 50
              getUserBasicInfoBatchSize: &entity-user-getUserBasicInfoBatchSize 50
              getUserBasicInfoTimeoutMs: &entity-user-getUserBasicInfoTimeoutMs 100
              stubs:
                getUserBasicInfo: &entity-user-stubs-getUserBasicInfo false
                getUserBasicInfoBatch: &entity-user-stubs-getUserBasicInfoBatch false
                getOaUsers: &entity-user-stubs-getOaUsers false
            toolingGatekeeper:
              url: &tooling-gatekeeper-url tooling-gatekeeper-grpc-api-headless.tooling.svc.cluster.local.:8080
              checkFeaturesBatchSize: &tooling-gatekeeper-checkFeaturesBatchSize 0 # buggy for now I (fjuif) do not know why yet
              checkFeaturesTimeoutMs: &tooling-gatekeeper-checkFeaturesTimeoutMs 10
              stubs:
                checkFeatures: &tooling-gatekeeper-stubs-checkFeatures ~
                getFeatures: &tooling-gatekeeper-stubs-getFeatures false
            toolingJobs:
              url: &tooling-jobs-go-url tooling-jobs-grpc-api-headless.tooling.svc.cluster.local:8080
            safetyCoreService:
              url: &safety-core-service-url safety-core-grpc-api-headless.safety.svc.cluster.local.:8080
            chatModerationService:
              url: &chat-moderation-service-url chat-moderation-grpc-api.chat:8080
            relationshipGraph:
              url: &relationship-graph-url relationship-graph-grpc-api-headless.relationship-graph.svc.cluster.local.:8080
              relationship-friend-relGraphDarkWritesPercentage: &relationship-friend-relGraphDarkWritesPercentage 100
              relationship-friend-relGraphDarkReadsPercentage: &relationship-friend-relGraphDarkReadsPercentage 1
              relationship-friend-relGraphSwitchReadsPercentage: &relationship-friend-relGraphSwitchReadsPercentage 100
            officialaccountsBackend:
              url: &officialaccounts-backend-url officialaccounts-backend-grpc-api-headless.officialaccounts.svc.cluster.local:8080
            entityPostService:
              url: &entity-post-service-url entity-post-grpc-api-headless.entity-post.svc.cluster.local:8080
              read-tags-from-entity-post-percent: &read-tags-from-entity-post-percent 1
            entityTopicService:
              url: &entity-topic-service-url entity-topic-grpc-api-headless.entity.svc.cluster.local:8080
            entityEventService:
              url: &entity-event-service-url entity-event-grpc-api-headless.entity.svc.cluster.local:8080
            eventBackendService:
              url: &event-backend-service-url event-backend-grpc-api-headless.event.svc.cluster.local:8080
            eventFrontendService:
              url: &event-frontend-service-url event-frontend-grpc-api.event.svc.cluster.local:8080
      
        safety:
          access-service: &default-safety-access-service
            vipUserIds:
              - 6qCz8SuCswWcUFbr7KmMpxuDs872 # attalgabriel
      
        amplitude:
          analytics-key: &amplitude-analytics-key sm://backend-core-amplitude-experiment-api-key
          deployment-key: &amplitude-deployment-key sm://backend-core-recap-notification-deployment-key
      
        database: &default-database
          type: postgres
          synchronize: false
          skipMigrations: false
          logging: false
          host: pgbouncer-master.bereal-prod.svc.cluster.local.
          port: 5432
          database: bereal
          username: bereal
          password: sm://backend-core-sql
          connectTimeoutMS: 30000 # 30s
          extra:
            min: 0
            max: 5
            statement_timeout: 10000
            query_timeout: 15000
      
        redis: &default-redis
          mode: cluster
          lazyConnect: false
          enableOfflineQueue: true
          scaleReads: all
          parallel: 100
      
        redis-standalone: &default-standalone-redis
          mode: standalone
          lazyConnect: false
          enableOfflineQueue: true
          port: 6379
      
        entity:
          entity-user-spanner: &entity-user-spanner
            projectId: "backend-core-prod"
            instanceId: "entity-user"
            databaseId: "entity-user"
          entity-user-init-spanner: &entity-user-init-spanner false
      
        persistent-posts-spanner: &persistent-posts-spanner
          projectId: backend-core-prod
          instanceId: persistent-posts
      
        feature-store-spanner: &feature-store-spanner
          projectId: backend-core-prod
          instanceId: feature-store
      
        cache: &default-cache
          type: "redis"
      
        metrics: &default-metrics
          microTaskQueueProbe: false
          libuvProbe: false
          requestsInFlight: true
      
        person-recently-user-suspension: &default-person-recently-user-suspension
          enabled: true
          refreshIntervalSec: 60
          durationMin: 120
      
        analytics:
          analytics-database: &analytics-database
            <<: *default-database
            host: db.analytics-events.bereal.lan
            database: events
            username: bereal
            password: sm://backend-core-sql-analytics-events
      
        archive:
          database-archive-post-shard: &archive-post-database-shard
            <<: *default-database
            connectionName: archive-post
            host: pgbouncer-archive-post.bereal-prod.svc.cluster.local.
            password: sm://backend-core-sql-archive-post
      
          database-archive-post: &archive-post-database
            type: "cluster"
            writeStrategy: shard
            readStrategy: shard
            shards:
              - <<: *archive-post-database-shard
                database: archive-post-0
              - <<: *archive-post-database-shard
                database: archive-post-1
              - <<: *archive-post-database-shard
                database: archive-post-2
              - <<: *archive-post-database-shard
                database: archive-post-3
              - <<: *archive-post-database-shard
                database: archive-post-4
              - <<: *archive-post-database-shard
                database: archive-post-5
              - <<: *archive-post-database-shard
                database: archive-post-6
              - <<: *archive-post-database-shard
                database: archive-post-7
              - <<: *archive-post-database-shard
                database: archive-post-8
              - <<: *archive-post-database-shard
                database: archive-post-9
      
          database-archive-post-shard-streamable:
            &archive-post-database-shard-streamable
            <<: *default-database
            username: archive-post
            password: sm://backend-core-sql-archive-post-user
            connectionName: archive-post-streamable
            extra:
              min: 0
              max: 5
      
          database-archive-post-streamable: &archive-post-database-streamable
            type: "cluster"
            writeStrategy: shard
            readStrategy: shard
            shards:
              - <<: *archive-post-database-shard-streamable
                host: db.archive-post-0.bereal.lan
                database: archive-post-0
              - <<: *archive-post-database-shard-streamable
                host: db.archive-post-1.bereal.lan
                database: archive-post-1
              - <<: *archive-post-database-shard-streamable
                host: db.archive-post-2.bereal.lan
                database: archive-post-2
              - <<: *archive-post-database-shard-streamable
                host: db.archive-post-3.bereal.lan
                database: archive-post-3
              - <<: *archive-post-database-shard-streamable
                host: db.archive-post-4.bereal.lan
                database: archive-post-4
              - <<: *archive-post-database-shard-streamable
                host: db.archive-post-5.bereal.lan
                database: archive-post-5
              - <<: *archive-post-database-shard-streamable
                host: db.archive-post-6.bereal.lan
                database: archive-post-6
              - <<: *archive-post-database-shard-streamable
                host: db.archive-post-7.bereal.lan
                database: archive-post-7
              - <<: *archive-post-database-shard-streamable
                host: db.archive-post-8.bereal.lan
                database: archive-post-8
              - <<: *archive-post-database-shard-streamable
                host: db.archive-post-9.bereal.lan
                database: archive-post-9
      
          archive-post-spanner: &archive-post-spanner
            <<: *persistent-posts-spanner
            databaseId: persistent-posts
          archive-post-comments-spanner: &archive-post-comments-spanner
            <<: *persistent-posts-spanner
            databaseId: persistent-post-comments
          archive-post-realmojis-spanner: &archive-post-realmojis-spanner
            <<: *persistent-posts-spanner
            databaseId: persistent-post-realmoji
          archive-post-tags-spanner: &archive-post-tags-spanner
            <<: *persistent-posts-spanner
            databaseId: persistent-post-tags
          spanner-comments-write-percentage: &spanner-comments-write-percentage 100
          post-service:
            shouldForceIndex: &should-force-index true
      
        memories-recap:
          memories-recap-spanner: &memories-recap-spanner
            projectId: backend-core-prod
            instanceId: "memories-recap"
            databaseId: "memories-recap"
          redis-memories-recap: &redis-memories-recap
            <<: *default-redis
            nodes:
              - host: redis-cluster.redis-recap-eoy-a.svc.cluster.local.
                port: 6379
            connectionName: redis-memories-recap
          cache-memories-recap: &cache-memories-recap
            <<: *default-cache
            name: cache-memories-recap
            redis: *redis-memories-recap
      
      
        chat:
          redis-chat: &redis-chat
            <<: *default-redis
            nodes:
              - host: redis-cluster.redis-chat-a.svc.cluster.local.
                port: 6379
            connectionName: redis-auth
          cache-chat: &cache-chat
            name: cache-chat
            <<: *default-cache
            redis: *redis-chat
      
        auth:
          auth-token-spanner: &auth-token-spanner
            projectId: backend-core-prod
            instanceId: auth
            databaseId: "auth-refresh-token"
            pool:
              min: 10
              max: 60
              maxIdle: 20
              incStep: 10
          redis-auth: &redis-auth
            <<: *default-redis
            nodes:
              - host: redis-cluster.redis-auth-a.svc.cluster.local.
                port: 6379
            connectionName: redis-auth
          cache-auth: &cache-auth
            name: cache-auth
            <<: *default-cache
            redis: *redis-auth
          session: &auth-session
            redis: *redis-auth
            cookieName: "bereal-auth"
            cookieSecret: sm://backend-core-auth-session-cookie-secret
            sessionPrefix: auth:session:v3
          database-auth: &auth-database
            <<: *default-database
            connectionName: database-auth
            replication:
              master:
                host: pgbouncer-core-auth.bereal-prod.svc.cluster.local.
                database: auth
                username: bereal
                password: sm://backend-core-sql-auth
              slaves:
                - host: pgbouncer-auth-rr-0.bereal-prod.svc.cluster.local.
                  database: auth
                  username: bereal
                  password: sm://backend-core-sql-auth
          database-auth-streamable: &auth-database-streamable
            <<: *default-database
            connectionName: database-auth-streamable
            host: db.auth.bereal.lan
            database: auth
            username: bereal
            password: sm://backend-core-sql-auth
      
          google:
            clientId: &auth-google-client-id ************-8unpa247j3i73rihra4mecrqi1j3512r.apps.googleusercontent.com
            clientSecret: &auth-google-client-secret sm://backend-core-auth-google-client-secret
      
          signin: &auth-signin
            publishCheckCodeTopic: true
            highRiskCountries:
              - UZ
              - RU
              - UA
              - BY
              - RO
              - KH
              - PK
              - VN
              - AZ
              - KG
            phoneNumbersToAuthBySlack:
              - "+33634636491" # Clara test
              - "+33695738624" # Clara mineur
              - "+447741689623" # UK number
              - "+12139876543" # US number
              - "+447759844444" # Florian Ganzin 1
              - "+0613943681" # Florian Ganzin 2
              - "+33634636494" # Fred
              - "+33600000000" # Charles Test
              - "+33600000001" # Cesar Test
              - "+33600000002" # Olive test
              - "+33600000003" # Victor test
              - "+33600000004" # Adam test
              - "+33600000005" # Aleksei Test
              - "+33600000006" # Botan Test
              - "+33600000007" # Chalom test
              - "+33600000008" # Chneor Test
              - "+33600000009" # Florent Bertrand Test
              - "+33600000010" # Jeremie Test
              - "+33600000011" # Kevin Ma test
              - "+33600000012" # Kevin Mo test
              - "+33600000013" # Lahssen test
              - "+33600000014" # Maximilien test
              - "+33600000015" # Michael Ustinov test
              - "+33600000016" # Michael Andrianarimanga test
              - "+33600000017" # Alexandre Jais
              - "+3**********" # Alexandre test
              - "+33600000019" # Quang test
              - "+33600000020" # Aymeric test
              - "+33600000021" # Elliot test
              - "+33600000022" # Florent Champigny test
              - "+33600000023" # Florian Chapuis test
              - "+33600000024" # Gaetan test
              - "+33600000025" # William test
              - "+33600000026" # Nicolas test
              - "+33600000027" # Dov test
              - "+33600000028" # SRE team test
            providers:
              static:
                restricted: true
                weight: 0
                phoneNumbers:
                  - phoneNumber: "+16505551234" # app store review
                    code: "293653"
                  - phoneNumber: "+33600000000" # play store review
                    code: "111222"
                  - phoneNumber: "+13475416216" # berealmobot1
                    code: "934716"
                  - phoneNumber: "+17328893261" # teammobot1
                    code: "934716"
                  - phoneNumber: "+17329245038" # mobotandroid3
                    code: "934716"
                  - phoneNumber: "+13323332056" # mobotandroid4
                    code: "934716"
                  - phoneNumber: "+16462768586" # berealmobot2
                    code: "934716"
                  - phoneNumber: "+16466283739" # teammobot2
                    code: "934716"
                  - phoneNumber: "+16466287101" # mobotandroid1
                    code: "934716"
                  - phoneNumber: "+16466283829" # mobotandroid2
                    code: "934716"
                  - phoneNumber: "+33634636481" # Bereal Mobile Candidate #2
                    code: "281405"
                  - phoneNumber: "+33634636482" # Bereal Mobile Candidate #3
                    code: "395713"
                  - phoneNumber: "+33690000000" # QA automation (@Karl & @Vlad)
                    code: "665616"
                  - phoneNumber: "+33690000001" # QA automation (@Karl & @Vlad)
                    code: "665616"
                  - phoneNumber: "+33690000002" # QA automation (@Karl & @Vlad)
                    code: "665616"
                  - phoneNumber: "+33690000003" # QA automation (@Karl & @Vlad)
                    code: "665616"
                  - phoneNumber: "+33690000004" # QA automation (@Karl & @Vlad)
                    code: "665616"
                  - phoneNumber: "+33690000005" # QA automation (@Karl & @Vlad)
                    code: "665616"
                  - phoneNumber: "+33690000006" # QA automation (@Karl & @Vlad)
                    code: "665616"
                  - phoneNumber: "+33690000007" # QA automation (@Karl & @Vlad)
                    code: "665616"
                  - phoneNumber: "+33690000009" # QA automation (@Karl & @Vlad)
                    code: "665616"
                  - phoneNumber: "+33690000010" # QA automation (@Karl & @Vlad)
                    code: "665616"
                  - phoneNumber: "+33690000011" # QA automation (@Karl & @Vlad)
                    code: "665616"
                  - phoneNumber: "+33690000012" # QA automation (@Karl & @Vlad)
                    code: "665616"
                  - phoneNumber: "+33690000013" # QA automation (@Karl & @Vlad)
                    code: "665616"
                  - phoneNumber: "+33690000014" # QA automation (@Karl & @Vlad)
                    code: "665616"
                  - phoneNumber: "+33690000015" # QA automation (@Karl & @Vlad)
                    code: "665616"
                  - phoneNumber: "+33690000016" # QA automation (@Karl & @Vlad)
                    code: "665616"
                  - phoneNumber: "+33690000017" # QA automation (@Karl & @Vlad)
                    code: "665616"
                  - phoneNumber: "+33690000018" # QA automation (@Karl & @Vlad)
                    code: "665616"
                  - phoneNumber: "+33690000019" # QA automation (@Karl & @Vlad)
                    code: "665616"
                  - phoneNumber: "+33690000020" # QA automation (@Karl & @Vlad)
                    code: "665616"
                  - phoneNumber: "+33690000021" # Security (@Jacob)
                    code: "491325"
                  - phoneNumber: "+33690000022" # Security (@Jacob)
                    code: "491325"
              slack:
                restricted: true
                weight: 0
                codeLength: 6
                codeValiditySeconds: 300
                slack:
                  channel: T07ELDMJ9/B090Y4059JP/mx84rfKWxPL6r07JM992KV2o # ask SRE to renew https://api.slack.com/apps/A07DTEK7FCP/general
              vonage:
                restricted: false
                apiKey: "6842a416" #default
                apiSecret: sm://backend-core-auth-vonage-api-key
                signinApiKey: "6842a416" # existing user api key
                signinApiSecret: sm://backend-core-auth-vonage-api-key
                signupHighRiskCountriesApiKey: "04dd9fdd" # new user from high risk countries
                signupHighRiskCountriesApiSecret: sm://backend-core-auth-vonage-signup-high-risk-countries-api-key
                signupLowRiskCountriesApiKey: "4cf1b28c" # new user from low risk countries
                signupLowRiskCountriesApiSecret: sm://backend-core-auth-vonage-signup-low-risk-countries-api-key
                weight: 1
                timeout: 5000
              ding:
                restricted: false
                customerUuid: e1fb7ce5-afca-43cd-8a74-dda58c1ac3ed
                secretToken: sm://backend-core-auth-ding-api-key
                weight: 99
                supportedCountries:
                  - AD
                  - AE
                  - AF
                  - AG
                  - AI
                  - AL
                  - AO
                  - AR
                  - AS
                  - AT
                  - AU
                  - AW
                  - AZ
                  - BA
                  - BB
                  - BD
                  - BE
                  - BF
                  - BG
                  - BH
                  - BI
                  - BJ
                  - BM
                  - BN
                  - BO
                  - BR
                  - BS
                  - BT
                  - BW
                  - BY
                  - BZ
                  - CA
                  - CD
                  - CF
                  - CG
                  - CH
                  - CI
                  - CK
                  - CL
                  - CN
                  - CO
                  - CR
                  - CV
                  - CW
                  - CY
                  - CY
                  - CZ
                  - DE
                  - DJ
                  - DK
                  - DM
                  - DO
                  - DZ
                  - EC
                  - EE
                  - EG
                  - ER
                  - ES
                  - FI
                  - FJ
                  - FK
                  - FM
                  - FO
                  - FR
                  - GA
                  - GB
                  - GD
                  - GE
                  - GF
                  - GG
                  - GI
                  - GL
                  - GM
                  - GP
                  - GQ
                  - GR
                  - GT
                  - GU
                  - GY
                  - HK
                  - HN
                  - HR
                  - HT
                  - HU
                  - ID
                  - IE
                  - IL
                  - IM
                  - IN
                  - IQ
                  - IS
                  - IT
                  - JE
                  - JM
                  - JP
                  - KG
                  - KH
                  - KI
                  - KM
                  - KN
                  - KR
                  - KW
                  - KY
                  - LA
                  - LB
                  - LC
                  - LI
                  - LK
                  - LS
                  - LT
                  - LU
                  - LV
                  - LY
                  - MA
                  - MC
                  - MD
                  - ME
                  - MG
                  - MK
                  - ML
                  - MM
                  - MN
                  - MO
                  - MQ
                  - MR
                  - MS
                  - MT
                  - MU
                  - MV
                  - MW
                  - MX
                  - MY
                  - MZ
                  - NA
                  - NC
                  - NE
                  - NF
                  - NI
                  - NL
                  - NO
                  - NP
                  - NR
                  - NZ
                  - OM
                  - PA
                  - PE
                  - PF
                  - PG
                  - PH
                  - PK
                  - PL
                  - PM
                  - PR
                  - PS
                  - PT
                  - PW
                  - PY
                  - QA
                  - RE
                  - RO
                  - RU
                  - SA
                  - SB
                  - SC
                  - SE
                  - SG
                  - SI
                  - SK
                  - SL
                  - SM
                  - SN
                  - SO
                  - SR
                  - ST
                  - SV
                  - SY
                  - TC
                  - TD
                  - TG
                  - TH
                  - TJ
                  - TL
                  - TM
                  - TN
                  - TO
                  - TR
                  - TT
                  - TW
                  - UA
                  - US
                  - UY
                  - UZ
                  - VC
                  - VE
                  - VG
                  - VN
                  - VU
                  - WF
                  - WS
                  - XK
                  - YE
                  - ZA
                  - ZW
                  - AM
              boomware:
                restricted: false
                weight: 0
                username: "03143db910f9d67a3f60fec4277c736e"
                password: "171581d9"
                supportedCountries:
                  - AZ
            throttling:
              ip:
                requests: 100
                window: 60
              phonenumber:
                requests: 5
                window: 60
              deviceId:
                requests: 5
                window: 60
              checkCode:
                requests: 5
                window: 60
            firebaseTokenKey: sm://backend-core-auth-firebase-verify-key
            prefixBlackList:
              - "6288229112"
              - "6288227662"
              - "6288229134"
              - "6288229152"
              - "6288229131"
              - "6288229154"
              - "6288229114"
              - "6288229110"
              - "6288229111"
              - "6288229105"
              - "6288227614"
              - "6288227612"
              - "6288229113"
              - "6288229132"
              - "6288229102"
              - "6288227613"
              - "6288229107"
              - "6288229101"
              - "6288229104"
              - "6288229103"
      
              - "6285212160"
              - "6282304651"
              - "6285212165"
              - "6285212166"
              - "6285212168"
              - "6282304653"
              - "6282304659"
              - "6285212161"
              - "6282304650"
              - "6282304657"
      
              - "3246875"
              - "20152629"
              - "20152428"
      
              - "254777441"
              - "6282222468"
              - "6285212161"
              - "6282222463"
              - "6282244458"
              - "6285212168"
      
              - "21261952"
              - "21261445"
              - "21266400"
      
              - "98936396"
              - "98901289"
              - "98903951"
              - "98905570"
              - "98930337"
              - "98933145"
              - "98939171"
              - "98937882"
      
              - "6392050729"
              - "639198329"
      
              - "63919832"
      
              - "639198329"
      
              - "37256101"
              - "37254468"
              - "37253293"
              - "37253352"
              - "639198329"
      
              - "9892207793"
              - "9892207781"
              - "9892207782"
              - "9892207783"
              - "9892207784"
              - "9892207786"
              - "9892207789"
              - "9892207791"
              - "9892207792"
              - "9892207794"
              - "9892207795"
              - "9892207796"
              - "9892207797"
              - "9892207798"
              - "9892207799"
              - "9892207787"
              - "9892207788"
              - "9892207785"
              - "9892207790"
              - "9892207780"
      
              - "218928514"
              - "218928515"
              - "218928517"
              - "218928511"
              - "218928510"
              - "218928519"
              - "218928516"
              - "218928513"
              - "218928518"
              - "218928512"
      
              - "989921290"
              - "989921298"
              - "989921297"
              - "989921294"
              - "989921292"
              - "989921296"
              - "989921299"
              - "989921293"
              - "989921295"
              - "989921291"
              - "989122227"
              - "989122223"
              - "989122225"
              - "989122224"
              - "989122221"
              - "989122228"
              - "989122226"
              - "989122229"
              - "989122222"
              - "989122220"
      
              - "63937864"
      
              - "63919832"
              - "************"
              - "32471924120"
              - "33614642935"
              - "13852305206"
              - "17156060093"
              - "************"
              - "32471924120"
      
              - "33633561016"
              - "************"
              - "14352510893"
              - "************"
              - "************"
              - "************"
              - "21696044263"
              - "************"
              - "12607012128"
              - "************"
              - "33614642935"
              - "13852305206"
              - "17156060093"
              - "************"
              - "32471924120"
      
          clients:
            &auth-clients # uuidgen | tr '[:lower:]' '[:upper:]' to generate a new key
            - client_id: android
              client_secret: F5A71DA-32C7-425C-A3E3-375B4DACA406
              life_time: 3600
              grant_types:
                - firebase
                - firebase_2fa
                - refresh_token
                - phone
            - client_id: ios
              client_secret: 962D357B-B134-4AB6-8F53-BEA2B7255420
              life_time: 3600
              grant_types:
                - firebase
                - firebase_2fa
                - refresh_token
                - phone
            - client_id: impersonation
              client_secret: 2630564E-6CE7-4D8B-9AD5-221A001B8061
              life_time: 600
              grant_types:
                - phone
            - client_id: ambassador-ui
              client_secret: 072965D5-9F9F-41E0-B771-78B8F14DF90B
              life_time: 3600
              redirect_urls:
                - https://tools.bereal.team
              scopes:
                - ambassador
              grant_types:
                - authorization_code
                - refresh_token
            - client_id: moment-ui
              client_secret: 7FD35892-D8F6-4FB7-8BF2-810015A8B46E
              life_time: 3600
              redirect_urls:
                - https://tools.bereal.team/moment
              roles:
                - admin
              scopes:
                - regions
                - moments
                - moments:admin
              grant_types:
                - authorization_code
                - refresh_token
            - client_id: regions-ui
              client_secret: D1A0B758-7258-44F3-9660-244BF981675B
              life_time: 3600
              redirect_urls:
                - https://tools.bereal.team/regions
              scopes:
                - regions
              grant_types:
                - authorization_code
                - refresh_token
            - client_id: deprecated-friendship-ui
              client_secret: DD897020-9305-46FF-9C5F-62D915AD8D17
              life_time: 3600
              redirect_urls:
                - https://tools.bereal.team/deprecated-friendship
              scopes:
                - deprecated-friendship
              grant_types:
                - authorization_code
                - refresh_token
            - client_id: profile-ui
              client_secret: B9CD863F-566B-4A6A-91A6-B61F5AC28988
              life_time: 3600
              redirect_urls:
                - https://tools.bereal.team/profile
              scopes:
                - profile
                - user:admin
              grant_types:
                - authorization_code
                - refresh_token
            - client_id: ambassador-ui
              client_secret: D0477A12-132E-44C9-BA60-4EDCDC18AEA4
              life_time: 3600
              redirect_urls:
                - https://tools.bereal.team/ambassadors
              scopes:
                - ambassador
                - ambassador:admin
              grant_types:
                - authorization_code
                - refresh_token
            - client_id: user-roles-ui
              client_secret: FFE61431-1EDB-4871-BE09-7CC54A086BAC
              life_time: 3600
              redirect_urls:
                - https://tools.bereal.team/user-roles
              scopes:
                - user:admin
              grant_types:
                - authorization_code
                - refresh_token
            - client_id: redis-ui
              client_secret: 33491281-9321-49cb-90b0-f94171fd05f8
              life_time: 3600
              redirect_urls:
                - https://tools.bereal.team/redis
              scopes:
                - redis
              grant_types:
                - authorization_code
                - refresh_token
            - client_id: doc-ui
              client_secret: 067950D9-75BD-4F20-A5B0-ACB0172391FE
              life_time: 3600
              redirect_urls:
                - https://doc.bereal.team
              scopes:
                - doc
              grant_types:
                - authorization_code
                - refresh_token
            - client_id: me-ui
              client_secret: 301F631B-AC2E-4166-877D-D86103365D66
              life_time: 3600
              redirect_urls:
                - https://tools.bereal.team/me
              scopes:
                - ambassador
                - ambassador:admin
                - regions
                - moments
                - moments:admin
                - profile
                - user:admin
                - doc
                - tooling-jobs
              grant_types:
                - authorization_code
                - refresh_token
            - client_id: tooling-job-runner-ui
              client_secret: 87E897C6-118F-4B96-A7F8-95D93A3214D3
              life_time: 3600
              redirect_urls:
                - https://tools.bereal.team/jobs
              scopes:
                - tooling-jobs
              grant_types:
                - authorization_code
                - refresh_token
            - client_id: auth-roles-ui
              client_secret: 5A0A8F77-4099-45E7-9C11-94416B4662BB
              life_time: 3600
              redirect_urls:
                - https://tools.bereal.team/roles
              scopes:
                - auth
              grant_types:
                - authorization_code
                - refresh_token
            - client_id: settings-ui
              client_secret: 33DACB54-03C8-4506-A4A6-33013266801F
              life_time: 3600
              redirect_urls:
                - https://tools.bereal.team/settings
              scopes:
                - settings
              grant_types:
                - authorization_code
                - refresh_token
            - client_id: gatekeeper-ui
              client_secret: A52503DE-46D2-4E76-B9AE-60DD47ADC76E
              life_time: 3600
              redirect_urls:
                - https://tools.bereal.team/gatekeeper
              scopes:
                - gatekeeper
              grant_types:
                - authorization_code
                - refresh_token
            - client_id: keda-scaler-ui
              client_secret: BD66F372-3CF5-4996-A807-06114E103CE7
              life_time: 3600
              redirect_urls:
                - https://tools.bereal.team/keda
              scopes:
                - settings
              grant_types:
                - authorization_code
                - refresh_token
            - client_id: postman
              client_secret: 828BABE9-8F23-4E02-8FB8-D7BC6CC53C51
              life_time: 3600
              firebase_token: true
              redirect_urls:
                - https://oauth.pstmn.io/v1
              scopes:
                - profile
                - moderation
              grant_types:
                - client_credentials
                - authorization_code
                - refresh_token
                - firebase
                - firebase_2fa
                - phone
            - client_id: zendesk
              client_secret: 828BABE9-8F23-4E02-8FB8-D7BC6CC53C51
            - client_id: bereal-web-ui
              client_secret: B7CDDFE7-9EB5-40DA-ADE9-B9B03BBB6678
              life_time: 3600
              redirect_urls:
                - https://tools.bereal.team/bereal-web
              scopes:
                - bereal-web
                - bereal-web:impersonate
              grant_types:
                - authorization_code
                - refresh_token
            - client_id: auth-user-ui
              client_secret: D6E831CD-AF0F-4FD9-8FDC-12AE518CAE99
              life_time: 3600
              redirect_urls:
                - https://tools.bereal.team/users
              scopes:
                - auth-user
              grant_types:
                - authorization_code
                - refresh_token
            - client_id: inapp-messages-segments-loader-ui
              client_secret: 70E6E991-816E-4683-AF0E-561471F23DAD
              life_time: 3600
              redirect_urls:
                - https://tools.bereal.team/segments-loader
              scopes:
                - inapp-messages
              grant_types:
                - authorization_code
                - refresh_token
            - client_id: tooling-qa-ui
              client_secret: 6f20541a-52e4-43f5-a3d4-4918b72f6d00
              life_time: 3600
              redirect_urls:
                - https://tools.bereal.team/qa
              scopes:
                - qa
              grant_types:
                - authorization_code
                - refresh_token
            - client_id: profile-ui-v2
              client_secret: 9c44fe47-170c-4f54-9210-867f13d1b69a
              life_time: 3600
              redirect_urls:
                - https://tools.bereal.team/profiles
              scopes:
                - profile
              grant_types:
                - authorization_code
                - refresh_token
      
        bereal:
          database-moments: &database-moments
            <<: *default-database
            connectionName: database-moment
            host: db.moment.bereal.lan
            database: moment
            username: bereal
            password: sm://backend-core-sql-moment
      
          redis-moment: &redis-moment
            <<: *default-redis
            connectionName: redis-moment
            nodes:
              - host: redis-cluster.redis-moment-a.svc.cluster.local.
                port: 6379
          cache-moment: &cache-moment
            name: cache-moment
            <<: *default-cache
            redis: *redis-moment
          region: &bereal-region
            regions:
              - code: us-central
                enableAutoAssign: true
                timezone: "America/Chicago"
                countries: ["GS"]
                hoursCoefs: [0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 8, 9, 10, 12, 12, 10, 9, 8, 8, 8, 3, 0, 0, 0]
                name:
                  en: Americas
                  fr: Amériques
                  es: América
                  de: Amerika
                  zh_CN: 美洲
                  ko: 북/남미
                  ja: アメリカ
                  nl: Amerika
                  pt: Americas
                  it: Americhe
              - code: europe-west
                enableAutoAssign: true
                timezone: "Europe/Paris"
                countries: ["CV"]
                hoursCoefs: [0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 6, 9, 10, 11, 10, 10, 9, 9, 9, 8, 6, 0, 0, 0]
                name:
                  en: Europe
                  fr: Europe
                  es: Europa
                  de: Europa
                  zh_CN: 欧洲
                  ko: 유럽
                  ja: ヨーロッパ
                  nl: Europa
                  pt: Europa
                  it: Europa
              - code: asia-west
                enableAutoAssign: true
                timezone: "Asia/Karachi"
                hoursCoefs: [0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 6, 9, 9, 10, 10, 9, 9, 9, 9, 9, 6, 2, 0, 0]
                name:
                  en: West Asia
                  fr: Asie de l'Ouest
                  es: Oeste Asiático
                  de: Westasien
                  zh_CN: 西亚
                  ko: 서아시아
                  ja: 西アジア
                  nl: West Azië
                  pt: Ásia Ocidental
                  it: Ovest Asia
              - code: asia-east
                enableAutoAssign: true
                timezone: "Australia/Sydney"
                hoursCoefs: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 7, 9, 12, 11, 10, 10, 10, 10, 9, 6, 3, 0, 0]
                name:
                  en: East Asia
                  fr: Asie de l'Est
                  es: Asia del Este
                  de: Ostasien
                  zh_CN: 东亚
                  ko: 동아시아
                  ja: 東アジア
                  nl: Oost Azië
                  pt: Ásia Oriental
                  it: Est Asia
              - code: dev1
                enableAutoAssign: false
                timezone: "Europe/Paris"
                hoursCoefs: [0, 0, 0, 0, 0, 0, 0, 0, 5, 8, 11, 11, 2, 5, 11, 11, 11, 11, 10, 9, 3, 0, 0, 0]
                name:
                  en: Dev Region 1
      
        common:
          chat-stream: &common-chat-stream
            apiKey: sm://backend-core-chat-stream-api-key
            apiKeySecret: sm://backend-core-chat-stream-api-key-secret
      
        feeds:
          database-feeds: &database-feeds
            type: postgres
            synchronize: false
            skipMigrations: false
            logging: false
            host: db.secondary.bereal.lan
            port: 5432
            database: feeds
            username: bereal
            password: sm://backend-core-sql-2
            extra:
              max: 10
            connectionName: feeds
          redis-discovery: &redis-discovery
            <<: *default-redis
            connectionName: redis-discovery
            nodes:
              - host: redis-cluster.redis-feeds-discovery-a.svc.cluster.local.
                port: 6379
          redis-memories: &redis-memories
            <<: *default-redis
            nodes:
              - host: redis-cluster.redis-feeds-memories-a.svc.cluster.local.
                port: 6379
            connectionName: redis-memories
          cache-memories: &cache-memories
            name: cache-memories
            <<: *default-cache
            redis: *redis-memories
          redis-memories-video: &redis-memories-video
            <<: *default-redis
            nodes:
              - host: redis-cluster.redis-feeds-memories-video-a.svc.cluster.local.
                port: 6379
            connectionName: redis-memories-video
          cache-memories-video: &cache-memories-video
            <<: *default-cache
            name: cache-memories-video
            redis: *redis-memories-video
          redis-feeds-friends: &redis-feeds-friends
            mode: cluster
            lazyConnect: false
            enableOfflineQueue: true
            scaleReads: all
            parallel: 100
            nodes:
              - host: redis-cluster.redis-feeds-friends-1.svc.cluster.local.
                port: 6379
            connectionName: redis-feeds-friends
            enableAutoPipelining: false
          redis-feeds-friends-v1: &redis-feeds-friends-v1
            mode: cluster
            lazyConnect: false
            enableOfflineQueue: true
            scaleReads: all
            parallel: 100
            nodes:
              - host: redis-cluster.redis-feeds-v1-a.svc.cluster.local.
                port: 6379
            connectionName: redis-feeds-friends-v1
            enableAutoPipelining: false
          redis-feeds-content: &redis-feeds-content
            mode: cluster
            lazyConnect: false
            enableOfflineQueue: true
            scaleReads: all
            parallel: 100
            nodes:
              - host: redis-cluster.redis-fee.svc.cluster.local.
                port: 6379
            connectionName: redis-feeds-content
            enableAutoPipelining: false
          cache-feeds-friends: &cache-feeds-friends
            name: cache-feeds-friends
            <<: *default-cache
            redis: *redis-feeds-friends
          cache-feeds-content: &cache-feeds-content
            name: cache-feeds-content
            <<: *default-cache
            redis: *redis-feeds-content
          redis-feeds-fof-feeds: &redis-feeds-fof-feeds
            <<: *default-redis
            connectionName: redis-feeds-fof-feeds
            nodes:
              - host: redis-cluster.redis-fof-feeds-a.svc.cluster.local.
                port: 6379
          cache-feeds-fof-feeds: &cache-feeds-fof-feeds
            <<: *default-cache
            name: cache-feeds-fof-feeds
            redis: *redis-feeds-fof-feeds
          feeds-friends-database: &feeds-friends-database
            <<: *default-database
            connectionName: feeds-friends
            host: db.feeds-friends.bereal.lan
            database: feeds-friends
            username: bereal
            password: sm://backend-core-sql-feeds-friends
      
        content:
          content-database: &content-database
            <<: *default-database
            connectionName: content
            replication:
              master:
                host: pgbouncer-content.bereal-prod.svc.cluster.local.
                database: content
                username: bereal
                password: sm://backend-core-sql-content
              slaves:
                - host: pgbouncer-content-rr-0.bereal-prod.svc.cluster.local.
                  database: content
                  username: bereal
                  password: sm://backend-core-sql-content
          content-database-streamable: &content-database-streamable
            <<: *default-database
            connectionName: content-stream
            host: db.content.bereal.lan
            database: content
            username: bereal
            password: sm://backend-core-sql-content
          database-content-post-credentials: &content-post-database-credentials
            username: bereal
            password: sm://backend-core-sql-content-post
          database-content-post-shard: &content-post-database-shard
            <<: *default-database
            username: bereal
            password: sm://backend-core-sql-content-post
            connectionName: content-post
            host: pgbouncer-content-post.bereal-prod.svc.cluster.local.
            database: content-post
            connectTimeoutMS: 30000 # 30s
            extra:
              min: 1
              max: 5
              statement_timeout: 10000
              query_timeout: 15000
          database-content-post-shard-raw-connection:
            &content-post-database-shard-raw-connection
            <<: *content-post-database-credentials
            type: postgres
            connectionName: content-post-raw-connection
            extra:
              min: 0
              max: 5
          content-post-database: &content-post-database
            <<: *content-post-database-shard
            type: "cluster"
            writeStrategy: shard
            readStrategy: shard
            shards:
              - <<: *content-post-database-shard
                host: pgbouncer-content-post-0.bereal-prod.svc.cluster.local.
                database: content-post-0
              - <<: *content-post-database-shard
                host: pgbouncer-content-post-1.bereal-prod.svc.cluster.local.
                database: content-post-1
              - <<: *content-post-database-shard
                host: pgbouncer-content-post-2.bereal-prod.svc.cluster.local.
                database: content-post-2
              - <<: *content-post-database-shard
                host: pgbouncer-content-post-3.bereal-prod.svc.cluster.local.
                database: content-post-3
              - <<: *content-post-database-shard
                host: pgbouncer-content-post-4.bereal-prod.svc.cluster.local.
                database: content-post-4
              - <<: *content-post-database-shard
                host: pgbouncer-content-post-5.bereal-prod.svc.cluster.local.
                database: content-post-5
              - <<: *content-post-database-shard
                host: pgbouncer-content-post-6.bereal-prod.svc.cluster.local.
                database: content-post-6
              - <<: *content-post-database-shard
                host: pgbouncer-content-post-7.bereal-prod.svc.cluster.local.
                database: content-post-7
              - <<: *content-post-database-shard
                host: pgbouncer-content-post-8.bereal-prod.svc.cluster.local.
                database: content-post-8
              - <<: *content-post-database-shard
                host: pgbouncer-content-post-9.bereal-prod.svc.cluster.local.
                database: content-post-9
          content-post-database-raw-connection: &content-post-database-raw-connection
            connectionName: content-post-raw-connection
            type: "cluster"
            writeStrategy: shard
            readStrategy: shard
            shards:
              - <<: *content-post-database-shard-raw-connection
                host: db.content-post-0.bereal.lan
                database: content-post-0
              - <<: *content-post-database-shard-raw-connection
                host: db.content-post-1.bereal.lan
                database: content-post-1
              - <<: *content-post-database-shard-raw-connection
                host: db.content-post-2.bereal.lan
                database: content-post-2
              - <<: *content-post-database-shard-raw-connection
                host: db.content-post-3.bereal.lan
                database: content-post-3
              - <<: *content-post-database-shard-raw-connection
                host: db.content-post-4.bereal.lan
                database: content-post-4
              - <<: *content-post-database-shard-raw-connection
                host: db.content-post-5.bereal.lan
                database: content-post-5
              - <<: *content-post-database-shard-raw-connection
                host: db.content-post-6.bereal.lan
                database: content-post-6
              - <<: *content-post-database-shard-raw-connection
                host: db.content-post-7.bereal.lan
                database: content-post-7
              - <<: *content-post-database-shard-raw-connection
                host: db.content-post-8.bereal.lan
                database: content-post-8
              - <<: *content-post-database-shard-raw-connection
                host: db.content-post-9.bereal.lan
                database: content-post-9
          content-comment-database: &content-comment-database
            <<: *default-database
            host: pgbouncer-content-comment.bereal-prod.svc.cluster.local.
            database: content-comment
            username: bereal
            password: sm://backend-core-sql-content-comment
            connectionName: content-comment
          database-content-realmoji-credentials:
            &content-realmoji-database-credentials
            username: bereal
            password: sm://backend-core-sql-content-realmoji
          database-content-realmoji-shard: &content-realmoji-database-shard
            <<: *default-database
            username: bereal
            password: sm://backend-core-sql-content-realmoji
            connectionName: content-realmoji
            host: pgbouncer-content-realmoji.bereal-prod.svc.cluster.local.
            database: content-realmoji
            extra:
              min: 1
              max: 5
              statement_timeout: 10000
              query_timeout: 15000
          content-realmoji-database: &content-realmoji-database
            <<: *content-realmoji-database-shard
            type: "cluster"
            writeStrategy: shard
            readStrategy: shard
            shards:
              - <<: *content-realmoji-database-shard
                host: pgbouncer-content-realmoji-0.bereal-prod.svc.cluster.local.
                database: content-realmoji-0
              - <<: *content-realmoji-database-shard
                host: pgbouncer-content-realmoji-1.bereal-prod.svc.cluster.local.
                database: content-realmoji-1
              - <<: *content-realmoji-database-shard
                host: pgbouncer-content-realmoji-2.bereal-prod.svc.cluster.local.
                database: content-realmoji-2
              - <<: *content-realmoji-database-shard
                host: pgbouncer-content-realmoji-3.bereal-prod.svc.cluster.local.
                database: content-realmoji-3
              - <<: *content-realmoji-database-shard
                host: pgbouncer-content-realmoji-4.bereal-prod.svc.cluster.local.
                database: content-realmoji-4
              - <<: *content-realmoji-database-shard
                host: pgbouncer-content-realmoji-5.bereal-prod.svc.cluster.local.
                database: content-realmoji-5
              - <<: *content-realmoji-database-shard
                host: pgbouncer-content-realmoji-6.bereal-prod.svc.cluster.local.
                database: content-realmoji-6
              - <<: *content-realmoji-database-shard
                host: pgbouncer-content-realmoji-7.bereal-prod.svc.cluster.local.
                database: content-realmoji-7
              - <<: *content-realmoji-database-shard
                host: pgbouncer-content-realmoji-8.bereal-prod.svc.cluster.local.
                database: content-realmoji-8
              - <<: *content-realmoji-database-shard
                host: pgbouncer-content-realmoji-9.bereal-prod.svc.cluster.local.
                database: content-realmoji-9
          content-screenshot-database: &content-screenshot-database
            <<: *default-database
            host: pgbouncer-content-screenshot.bereal-prod.svc.cluster.local.
            database: content-screenshot
            username: bereal
            password: sm://backend-core-sql-content-screenshot
            connectionName: content-screenshot
          content-tag-database: &content-tag-database
            <<: *default-database
            connectionName: content-tag
            host: pgbouncer-content-tag.bereal-prod.svc.cluster.local.
            database: content-tag
            username: content-tag
            password: sm://backend-core-sql-content-tag-user
          content-tag-database-streamable: &content-tag-database-streamable
            <<: *content-tag-database
            connectionName: content-tag-streamable
            host: db.content-tag.bereal.lan
          redis-content: &redis-content
            <<: *default-redis
            connectionName: redis-content
            nodes:
              - host: redis-cluster.redis-content-a.svc.cluster.local.
                port: 6379
          cache-content: &cache-content
            name: cache-content
            <<: *default-cache
            redis: *redis-content
          redis-content-realmoji-notification: &redis-content-realmoji-notification
            <<: *default-redis
            connectionName: redis-content-realmoji-notification
            nodes:
              - host: redis-cluster.redis-realmoji-notification-a.svc.cluster.local.
                port: 6379
          cache-content-realmoji-notification: &cache-content-realmoji-notification
            <<: *default-cache
            name: cache-content-realmoji-notification
            redis: *redis-content-realmoji-notification
          post-service:
            maxPostsPerMoment: &maxPostsPerMoment 10
            entityPostDarkWritesPercentage: 100
            entityPostReadPercentage: &entityPostReadPercentage 100
          recap-notification-experiment: &recap-notification-experiment
            deploymentKey: *amplitude-deployment-key
            analyticsKey: *amplitude-analytics-key
            experimentFlagKey: recap-notif-apr-2024
            subjectLineExperimentFlagKey: recap-notif-subject-line-optimization
            controlVariant: control
          recap-notification-default-params: &recap-notification-default-params
            minutesAfterMomentNotification: 5
            minutesAfterLastActivity: 5
            backOffFactorForLastActivity: 1.4
            maxMinutesAfterLastActivity: 60
            minutesAfterLastSent: 5
            backOffFactorForLastSent: 1.4
            maxMinutesAfterLastSent: 60
            maxRecapNotificationSent: 20
            minPostsToQualify: 1
            pushSettingsDefaultState: "true"
          recap-notification-experiment-use-gatekeeper: &recap-notification-experiment-use-gatekeeper false
          redis-content-recap-notification: &redis-content-recap-notification
            <<: *default-redis
            connectionName: redis-content-recap-notification
            nodes:
              - host: redis-cluster.redis-recap-notification-a.svc.cluster.local.
                port: 6379
          cache-content-recap-notification: &cache-content-recap-notification
            <<: *default-cache
            name: cache-content-recap-notification
            redis: *redis-content-recap-notification
          recap-notification-scheduler-service-users-partition-count: &recap-notification-scheduler-service-users-partition-count 256
          recap-notification-scheduler-service-minutes-per-bucket: &recap-notification-scheduler-service-minutes-per-bucket 5
          content-streak-reminders-experiment: &content-streak-reminders-experiment
            deploymentKey: *amplitude-deployment-key
            analyticsKey: *amplitude-analytics-key
          redis-content-streak-reminders: &redis-content-streak-reminders
            <<: *default-redis
            connectionName: redis-content-streak-reminders
            nodes:
              - host: redis-cluster.redis-streaks-a.svc.cluster.local.
                port: 6379
          content-streak-reminders-service-users-partition-count: &content-streak-reminders-service-users-partition-count 128
      
        notifications:
          notification-database-streamable: &notification-database-streamable
            <<: *default-database
            connectionName: notification
            host: db.notification.bereal.lan
            database: notification
            username: bereal
            password: sm://backend-core-sql-notification
          notification-database: &notification-database
            <<: *default-database
            connectionName: notification
            replication:
              master:
                host: pgbouncer-notification.bereal-prod.svc.cluster.local.
                database: notification
                username: bereal
                password: sm://backend-core-sql-notification
              slaves:
                - host: pgbouncer-notification-rr-0.bereal-prod.svc.cluster.local.
                  database: notification
                  username: bereal
                  password: sm://backend-core-sql-notification
          redis-notification: &redis-notification
            mode: cluster
            lazyConnect: false
            enableOfflineQueue: true
            scaleReads: all
            parallel: 100
            nodes:
              - host: redis-cluster.redis-notification-a.svc.cluster.local.
                port: 6379
            connectionName: redis-notification
          cache-notification: &cache-notification
            name: cache-notification
            <<: *default-cache
            redis: *redis-notification
      
        person:
          redis-person: &redis-person
            <<: *default-redis
            connectionName: redis-person
            nodes:
              - host: redis-cluster.redis-person-v1-a.svc.cluster.local.
                port: 6379
          cache-person: &cache-person
            name: cache-person
            <<: *default-cache
            redis: *redis-person
          person-database: &person-database
            <<: *default-database
            replication:
              master:
                host: pgbouncer-person.bereal-prod.svc.cluster.local.
                database: person
                username: bereal
                password: sm://backend-core-sql-person
              slaves:
                - host: pgbouncer-person-rr-0.bereal-prod.svc.cluster.local.
                  database: person
                  username: bereal
                  password: sm://backend-core-sql-person
                - host: pgbouncer-person-rr-1.bereal-prod.svc.cluster.local.
                  database: person
                  username: bereal
                  password: sm://backend-core-sql-person
          person-database-streamable: &person-database-streamable
            <<: *person-database
            host: db.person.bereal.lan
            username: bereal
            password: sm://backend-core-sql-person
            database: person
          person-user-suspensions-database: &person-user-suspensions-database
            <<: *person-database
            connectionName: person-user-suspensions
          user-service:
            enableUserFreshness: &enableUserFreshness true
            userFreshnessCheckPct: &userFreshnessCheckPct 100
          person-user-state-spanner: &user-state-spanner
            projectId: backend-core-prod
            instanceId: feature-store
            databaseId: "user-labels"
          user-suspension-service:
            recentlyUserSuspension: *default-person-recently-user-suspension
          collect-gender-pct: &collect-gender-pct 100
      
        moderation:
          database-moderation: &database-moderation
            <<: *default-database
            host: pgbouncer-moderation.bereal-prod.svc.cluster.local.
            database: moderation
            username: bereal
            password: sm://backend-core-sql-moderation
          moderation-report-database: &moderation-report-database
            <<: *database-moderation
            connectionName: moderation-report
            host: pgbouncer-moderation-report.bereal-prod.svc.cluster.local.
            database: moderation-report
            username: bereal
            password: sm://backend-core-sql-moderation-report
      
        music:
          spotify: &music-spotify
            clientId: sm://backend-core-spotify-client-id
            clientSecret: sm://backend-core-spotify-client-secret
            spotifyTokenApiUrl: https://accounts.spotify.com/api/token
            spotifyAuthorizeUrl: https://accounts.spotify.com/authorize
            oauthCallbackUrl: https://mobile-l7.bereal.com/api/music/spotify/token
            appRedirectUri: bere.al://auth/spotify-login-callback
      
          redis-music: &redis-music
            <<: *default-redis
            connectionName: redis-music
            nodes:
              - host: redis-cluster.redis-tooling-a.svc.cluster.local.
                port: 6379
      
        partner:
          partner-zendesk-database: &partner-zendesk-database
            <<: *database-moderation
            connectionName: partner-zendesk
            host: pgbouncer-partner-zendesk-0.bereal-prod.svc.cluster.local.
            database: partner-zendesk-0
            username: partner-zendesk
            password: sm://backend-core-sql-partner-zendesk
      
        officialaccounts:
          oa-list-service: &oa-list-service
            cacheRefreshIntervalSeconds: 10
      
        radioactive:
          radioactive-database: &radioactive-database
            <<: *default-database
            connectionName: radioactive-radioactive
            host: db.radioactive-radioactive-0.bereal.lan
            database: radioactive
            username: radioactive-radioactive
            password: sm://backend-core-sql-radioactive-radioactive
      
        recommendations:
          recommendations-friends-database: &recommendations-friends-database
            <<: *default-database
            connectionName: recommendations-friends
            replication:
              master:
                host: pgbouncer-friend-recommendations.bereal-prod.svc.cluster.local.
                database: friend-recommendations
                username: friend-recommendations
                password: sm://backend-core-sql-friend-recommendations
              slaves:
                - host: pgbouncer-friend-recommendations-rr-0.bereal-prod.svc.cluster.local.
                  database: friend-recommendations
                  username: friend-recommendations
                  password: sm://backend-core-sql-friend-recommendations
                - host: pgbouncer-friend-recommendations-rr-1.bereal-prod.svc.cluster.local.
                  database: friend-recommendations
                  username: friend-recommendations
                  password: sm://backend-core-sql-friend-recommendations
                - host: pgbouncer-friend-recommendations-rr-2.bereal-prod.svc.cluster.local.
                  database: friend-recommendations
                  username: friend-recommendations
                  password: sm://backend-core-sql-friend-recommendations
          recommendations-friends-database-streamable:
            &recommendations-friends-database-streamable
            <<: *default-database
            connectionName: recommendations-friends-streamable
            host: db.friend-recommendations.bereal.lan
            database: friend-recommendations
            username: friend-recommendations
            password: sm://backend-core-sql-friend-recommendations
          redis-recommendations-friends: &redis-recommendations-friends
            <<: *default-redis
            connectionName: redis-recommendations-friends
            nodes:
              - host: redis-cluster.redis-friend-recommendations-1-a.svc.cluster.local.
                port: 6379
          cache-recommendations-friends: &cache-recommendations-friends
            <<: *default-cache
            name: cache-recommendations-friends
            redis: *redis-recommendations-friends
      
        ranking:
          friend-ranking-spanner: &friend-ranking-spanner
            <<: *feature-store-spanner
            databaseId: "friendship-ranking"
          applyRankingSchemas: false # only create them in local/dev. They're created externally in prod
          default-friend-ranking-alg: &default-friend-ranking-alg "friend_rank_sent_engagements_normalized"
          friend-ranking-tables: &friend-ranking-tables
            - tableName: "friend_rank_sent_engagements_normalized"
              rankingAlg: "friend_rank_sent_engagements_normalized"
            - tableName: "friend_rank_sent_engagements_normalized_14_day"
              rankingAlg: "friend_rank_sent_engagements_normalized_14_day"
            - tableName: "friend_rank_sent_engagements_normalized_7_day"
              rankingAlg: "friend_rank_sent_engagements_normalized_7_day"
            - tableName: "friend_rank_sent_engagements_normalized_high_exponential_weighted"
              rankingAlg: "friend_rank_sent_engagements_normalized_high_exponential_weighted"
            - tableName: "friend_rank_sent_engagements_normalized_medium_exponential_weighted"
              rankingAlg: "friend_rank_sent_engagements_normalized_medium_exponential_weighted"
            - tableName: "friend_rank_sent_engagements_normalized_low_exponential_weighted"
              rankingAlg: "friend_rank_sent_engagements_normalized_low_exponential_weighted"
            - tableName: "friend_rank_sent_engagements_normalized_discrete_weighted"
              rankingAlg: "friend_rank_sent_engagements_normalized_discrete_weighted"
      
        relationship:
          rel-graph-hidden-read-base-pct: &rel-graph-hidden-read-base-pct 1
          relationship-graph-friends-write: &relationship-graph-friends-write true
          relationship-graph-friends-read-pct: &relationship-graph-friends-read-pct 1
          entity-user-friends-user-read-pct: &entity-user-friends-user-read-pct 1
          save-friends-deletes: &save-friends-deletes false
          relationship-graph-friends-double-read-pct: &relationship-graph-friends-double-read-pct 0
      
          relationship-database: &relationship-database
            <<: *default-database
            connectionName: relationship
            replication:
              master:
                host: pgbouncer-relationship.bereal-prod.svc.cluster.local.
                port: 5432
                database: relationship
                username: bereal
                password: sm://backend-core-sql-relationship
              slaves:
                - host: pgbouncer-relationship-rr-0.bereal-prod.svc.cluster.local.
                  database: relationship
                  username: bereal
                  password: sm://backend-core-sql-relationship
          relationship-database-streamable: &relationship-database-streamable
            <<: *default-database
            connectionName: relationship-streamable
            host: db.relationship.bereal.lan
            database: relationship
            username: bereal
            password: sm://backend-core-sql-relationship
          redis-relationship-contacts: &redis-relationship-contacts
            <<: *default-redis
            connectionName: redis-relationship-contacts
            nodes:
              - host: redis-cluster.redis-relationship-contacts-a.svc.cluster.local.
                port: 6379
          cache-relationship-contacts: &cache-relationship-contacts
            name: cache-relationship-contacts
            <<: *default-cache
            redis: *redis-relationship-contacts
      
          redis-relationship-friends: &redis-relationship-friends
            <<: *default-redis
            connectionName: redis-relationship-friends
            nodes:
              - host: redis-cluster.redis-relationship-friends-1-a.svc.cluster.local.
                port: 6379
      
          redis-relationship-users: &redis-relationship-users
            <<: *default-redis
            connectionName: redis-relationship-users
            nodes:
              - host: redis-cluster.redis-relationship-users-a.svc.cluster.local.
                port: 6379
      
          redis-relationship-suggestions: &redis-relationship-suggestions
            <<: *default-redis
            connectionName: redis-relationship-suggestions
            nodes:
              - host: redis-cluster.redis-relationship-suggestions-a.svc.cluster.local.
                port: 6379
      
        search:
          search-database: &search-database
            <<: *default-database
            connectionName: search
            replication:
              master:
                host: pgbouncer-search.bereal-prod.svc.cluster.local.
                database: search
                username: bereal
                password: sm://backend-core-sql-search
              slaves:
                - host: pgbouncer-search-rr-0.bereal-prod.svc.cluster.local.
                  database: search
                  username: bereal
                  password: sm://backend-core-sql-search
          redis-search: &redis-search
            <<: *default-redis
            nodes:
              - host: redis-cluster.redis-search-a.svc.cluster.local.
                port: 6379
            connectionName: redis-search
          cache-search: &cache-search
            name: cache-search
            <<: *default-cache
            redis: *redis-search
      
        settings:
          settings-database: &settings-database
            <<: *default-database
            connectionName: settings-settings-0
            host: pgbouncer-settings-settings-0.bereal-prod.svc.cluster.local.
            database: settings-settings-0
            username: settings-settings
            password: sm://backend-core-sql-settings-settings
          settings-database-streamable: &settings-database-streamable
            <<: *settings-database
            connectionName: settings-settings-0-streamable
            host: db.settings-settings-0.bereal.lan
            database: settings-settings-0
            username: settings-settings
            password: sm://backend-core-sql-settings-settings
          redis-settings: &redis-settings
            <<: *default-redis
            nodes:
              - host: redis-cluster.redis-settings-a.svc.cluster.local.
                port: 6379
            connectionName: redis-settings-a
          cache-settings: &cache-settings
            name: cache-settings
            <<: *default-cache
            redis: *redis-settings
      
        terms:
          redis-terms: &redis-terms
            <<: *default-redis
            nodes:
              - host: redis-cluster.redis-terms-a.svc.cluster.local.
                port: 6379
            connectionName: redis-terms
          cache-terms: &cache-terms
            <<: *default-cache
            name: cache-terms
            redis: *redis-terms
      
        tooling:
          redis-throttling: &redis-throttling
            connectionName: redis-throttling
            mode: cluster
            lazyConnect: false
            enableOfflineQueue: true
            scaleReads: all
            parallel: 2
            nodes:
              - host: redis-cluster.redis-throttling-a.svc.cluster.local.
                port: 6379
          redis-tooling: &redis-tooling
            <<: *default-redis
            connectionName: redis-tooling
            nodes:
              - host: redis-cluster.redis-tooling-a.svc.cluster.local.
                port: 6379
          cache-tooling: &cache-tooling
            name: cache-tooling
            <<: *default-cache
            redis: *redis-tooling
      
          redis-session: &redis-session
            <<: *default-redis
            connectionName: redis-session
            sessionPrefix: tooling:session
            nodes:
              - host: redis-cluster.redis-session-a.svc.cluster.local.
                port: 6379
          database-tooling: &database-tooling
            <<: *default-database
          tooling-activity-log-database: &tooling-activity-log-database
            <<: *default-database
            connectionName: tooling-activity-log
            host: db.tooling-activity-log.bereal.lan
            database: tooling-activity-log
            username: bereal
            password: sm://backend-core-sql-tooling-activity-log
      
          gatekeeper-enablePgFallback: &tooling-gatekeeper-enablePgFallback false
          gatekeeper-features: &tooling-gatekeeper-features
            - name: analytics
              openedCountries:
                - IS # Iceland
            - name: friendsOfFriendsFeed
              activated: true
            - name: messagingNext
              openedCountries:
                - IE # Ireland
                - IM # Isle of Man
                - CA # Canada
                - AU # Australia
            - name: realtimeStream
              openedCountries:
                - IE # Ireland
                - IM # Isle of Man
                - CA # Canada
                - AU # Australia
            - name: music
              activated: true
            - name: musicProviderSpotify
              activated: true
            - name: musicProviderApple
              activated: true
            - name: veeOne
              activated: true
            - name: queenstown
              activated: false
            - name: notilytics
              activated: false
            - name: discoveryFeed
              activated: false
          just-words-service:
            orgId: &just-words-service-org-id sm://backend-core-just-words-service-org-id
            sdkKey: &just-words-service-sdk-key sm://backend-core-just-words-service-sdk-key
      
        serviceAccount: &default-service-account
          projectId: "backend-core-prod"
      
        recaptcha-service-account: &recaptcha-service-account
          projectId: "backend-core-prod"
      
        firebase-account: &default-firebase-account
          projectId: "alexisbarreyat-bereal"
          credentials:
            client_email: "<EMAIL>"
            private_key: sm://backend-core-sa-fasterstore
      
        firebase: &default-firebase
          serviceAccount: *default-firebase-account
      
        auth-service:
          jwtSecret: &default-jwt-secret sm://backend-core-auth-jwt-key
          authEndpoint: &default-oauth-auth-endpoint https://auth.bereal.team/auth
          tokenEndpoint: &default-oauth-token-endpoint https://auth.bereal.team/token
      
        authentication: &default-authentication
          firebase:
            serviceAccount: *default-firebase-account
          jwtSecrets:
            - *default-jwt-secret
          userValidationEnabled: true
      
        oauth-client: &default-oauth-client
          securedPath:
            - "*"
          authorizeUri: *default-oauth-auth-endpoint
          tokenUri: *default-oauth-token-endpoint
      
        webapp-session: &default-webapp-session
          redis: *redis-session
      
        throttling: &default-throttling
          redis: *redis-throttling
          enable: true
          ip:
            max: ********
            ttl: 60
          user:
            max: 3000
            ttl: 60
      
        request-signature: &request-signature
          enabled: false
          secret: "sm://backend-core-common-api-request-signature-secret"
          enforce: false
          exclude:
            - "/status"
            - "/version"
            - "/metrics"
            - "/configurations"
      
        app: &default-app
          host: 0.0.0.0
          port: 8080
          name: bereal-app
          apiPrefix: api
          context: true
          security: true
          health: true
          logger: *default-logger
          requestSignature: *request-signature
          concurrency:
            checkInterval: 500
          prometheus:
            enable: true
            expose: true
          keepAliveTimeout: 300000
          headersTimeout: 60000
          throttling: *default-throttling
          metrics: *default-metrics
          gracePeriodMs: 10000
      
        grpc-app: &default-grpc-app
          host: 0.0.0.0
          port: 8080
          technicalPort: 3001
          logger: *default-logger
          gracePeriodMs: 20000
          type: "grpc"
      
        secret-manager: &default-secret-manager
          serviceAccount: *default-service-account
          defaultPrefix: "projects/************/secrets/"
      
        pub: &default-pub
          redis: *redis-tooling
          consume: false
          serviceAccount: *default-service-account
      
        sub: &default-sub
          redis: *redis-tooling
          consume: true
          serviceAccount: *default-service-account
      
        validator: &default-validator
          whitelist: true
          forbidNonWhitelisted: true
      
        serviceAccountUpload: &service-account-upload
          projectId: "backend-core-prod"
          credentials:
            client_email: "<EMAIL>"
            private_key: sm://backend-core-sa-fasterstore
      
        default-bereal-bucket: &default-bereal-bucket
          name: &default-bucket-name storage.bere.al
          serviceAccount: *service-account-upload
          useLegacyAcl: true
      
        us1-bucket: &us1-bucket
          name: &us1-bucket-name us1-storage.bere.al
          serviceAccount: *service-account-upload
      
        default-memories-bucket: &default-memories-bucket
          name: *default-bucket-name
          serviceAccount: *default-service-account
      
        memories-video-bucket: &memories-video-bucket
          name: bereal-us-central1-memories
          serviceAccount: *default-service-account
      
        default-content-post-bucket: &default-content-post-bucket
          name: *default-bucket-name
          serviceAccount: *service-account-upload
      
        default-content-post-storage: &default-content-post-storage
          name: content-post
          selector:
            method: direct
            bucket: *default-content-post-bucket
      
        default-content-realmoji-bucket: &default-content-realmoji-bucket
          name: *default-bucket-name
          serviceAccount: *service-account-upload
      
        default-content-realmoji-storage: &default-content-realmoji-storage
          name: content-realmoji
          selector:
            method: direct
            bucket: *default-content-realmoji-bucket
      
        default-person-realmoji-bucket: &default-person-realmoji-bucket
          name: *default-bucket-name
          serviceAccount: *service-account-upload
      
        default-person-realmoji-storage: &default-person-realmoji-storage
          name: person-realmoji
          selector:
            method: direct
            bucket: *default-person-realmoji-bucket
      
        default-person-bucket: &default-person-bucket
          name: *default-bucket-name
          serviceAccount: *service-account-upload
      
        default-person-storage: &default-person-storage
          name: person
          selector:
            method: direct
            bucket: *default-person-bucket
      
        default-arbitrary-storage: &default-arbitrary-storage
          name: arbitrary
          selector:
            method: arbitrary
            serviceAccount: *default-service-account
      
        default-benchmark-storage: &default-benchmark-storage
          name: benchmark
          selector:
            method: direct
            bucket: *default-bereal-bucket
      
        multibucket-storage: &multibucket-storage
          name: multibucket-storage
          selector:
            method: multibucket
            defaultBucket: *us1-bucket
            fallbackBucket: *default-bereal-bucket
            buckets:
              - *default-bereal-bucket
              - *us1-bucket
      
        # multibucket config here because
        #   in 2021 we uploaded the memories video on the default bereal bucket
        #   in 2022 we uploaded the memories video on a dedicated bucket
        memories-video-multibucket-storage: &memories-video-multibucket-storage
          name: memories-video-multibucket-storage
          selector:
            method: multibucket
            defaultBucket: *default-bereal-bucket
            fallbackBucket: *default-bereal-bucket
            buckets:
              - *default-bereal-bucket
              - *memories-video-bucket
      
        default-bereal-storage: &default-bereal-storage
          name: bereal
          selector:
            method: direct
            bucket: *default-bereal-bucket
      
        default-memories-storage: &default-memories-storage
          name: memories
          selector:
            method: direct
            bucket: *default-memories-bucket
      
        memories-video-storage: &memories-video-storage
          name: memories-video
          selector:
            method: direct
            bucket: *memories-video-bucket
      
        gdpr-bucket: &gdpr-bucket
          name: &gdpr-bucket-name bereal-us-central1-gdpr-export
          serviceAccount: *default-service-account
      
        gdpr-storage: &gdpr-storage
          name: gdpr
          selector:
            method: direct
            bucket: *gdpr-bucket
      
        chat-media-bucket: &chat-media-bucket
          name: &chat-media-bucket-name bereal-us-central1-chat-media
          serviceAccount: *default-service-account
      
        chat-media-storage: &chat-media-storage
          name: chat-media
          selector:
            method: direct
            bucket: *chat-media-bucket
      
        default-storagePresenter: &default-storage-presenter
          logger: *default-logger
          defaultBucket:
            name: storage.bere.al
            url: https://cdn.bereal.network/
          buckets:
            - name: alexisbarreyat-bereal.appspot.com
              url: https://cdn.bereal.network/
              resizedUrl: https://cdn-resize.bereal.network/
              processor: cloudflare
            - name: bereal-us-central1-gdpr-export
              url: https://gdpr.bere.al/
            - name: storage.bere.al
              resizedUrl: https://cdn-resize.bereal.network/
              processor: cloudflare
              cdn:
                - url: https://cdn.bereal.network/
                  weight: 999
                - url: https://cdn-mc-eu1-fd5f74b2.bereal.network/
                  weight: 1
            - name: bereal-us-central1-memories
              url: https://cdn-memories.bereal.network/
            - name: us1-storage.bere.al
              url: https://cdn-us1.bereal.network/
      
        default-notification-push-bucket: &default-notification-push-bucket
          name: *default-bucket-name
          serviceAccount: *default-service-account
      
        default-notification-push-storage: &default-notification-push-storage
          name: notification-push
          selector:
            method: direct
            bucket: *default-notification-push-bucket
      
        job: &default-job
          redis: *redis-tooling
          pubSub: *default-pub
          logger: *default-logger
          database: &job-database
            <<: *default-database
      
        default-realpush: &default-realpush
          token: &default-realpush-token sm://backend-core-realpush-token
      
        maxPostDeletion: &max-post-deletion 5
      
        default-chat:
          moderation-service: &default-chat-moderation-service
            apiUrl: chat-moderation-grpc-api.chat:8080
      
        new-memories-path-users: &new-memories-path-users
          enabledUserIds:
            - wPnzje0YkLYPXcqYjEVjpW7Gdhd2 # kyle
          defaultState: "disabled"
      
      datadog-tracer:
        enabled: true
        logInjection: true
        runtimeMetrics: true
        profiling: false
      
      secret-manager: *default-secret-manager
      
      validator:
        whitelist: true
        forbidNonWhitelisted: true
    application.conf.yaml: |-
      application:
        <<: *default-app
        name: archive-post-worker
      
      pod:
        namespace: archive
        domain: archive
        feature: post
        name: archive-post-worker
        type: worker
        registry: us-central1-docker.pkg.dev/shared-build-all/bereal
        command: domains/archive/post-worker/main.js
        version: "faafc67"
        image: bereal-monolith
        env: prod
        envRoot: prod
      build-info:
        app: archive-post-worker
        version: "faafc67"
        branch: main
        commit: "faafc67"
        env: prod
        envRoot: prod
      #  buildTime:
      
      ingress:
        enable: true
        suffix:
      strategy:
        rollingUpdate:
          maxSurge: "10%"
          maxUnavailable: "5%"
