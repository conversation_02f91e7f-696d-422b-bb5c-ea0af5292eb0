application:
  <<: *default-app
  name: bereal-moment-worker

bereal:
  moment-job:
    slack:
      channel: T07ELDMJ9/B091H7AKCCR/lRFI2tzNAB6874aR9Zp3sy2o
  moment-service:
    scheduleDaysInAdvance: 10

pod:
  namespace: bereal-prod
  domain: bereal
  feature: moment
  name: bereal-moment-worker
  type: worker
  registry: us-central1-docker.pkg.dev/shared-build-all/bereal
  command: domains/bereal/moment-worker/main.js
  version: "faafc67"
  image: bereal-monolith
  env: prod
  envRoot: prod
build-info:
  app: bereal-moment-worker
  version: "faafc67"
  branch: main
  commit: "faafc67"
  env: prod
  envRoot: prod
#  buildTime:

ingress:
  enable: true
  suffix:
strategy:
  rollingUpdate:
    maxSurge: "10%"
    maxUnavailable: "5%"
