# This file is generated by _tools/scripts/genconfig.sh; DO NOT EDIT.
backend-apps:
  configFiles:
    platform.conf.yaml: |-
      templates:
        logger: &default-logger
          level: debug
          mode: json
      
        # microservices is not injected in application config per se, it acts as a placeholder
        # for gathering services' adresses. Anchoring (&blabla) is heavily used to inject
        # the values everywhere it's needed.
        microservices:
          # some doc can be found here https://pkg.go.dev/google.golang.org/grpc/keepalive#ClientParameters
          keepalive: &grpc-default-keepalive ~
          # path in the container, you can find the workdir declared here: _tools/docker/monolith/Dockerfile
          protoBasePath: &proto-base-path "../../../backend-proto/"
          maxConnectionAgeMs: &grpc-max-connection-age-ms 30_000 # 30s
          maxConnectionAgeGraceMs: &grpc-max-connection-age-grace-ms 10_000 # 10s
          services:
            entityUser:
              url: &entity-user-url entity-user-grpc-api-headless.entity.svc.cluster.local.:8080
              flags: &entity-user-flags
                enableUpdations: true
                enableInsertions: true
                enableDeletions: true
              maxBatchSize: &entity-user-maxBatchSize 50
              getUserBasicInfoBatchSize: &entity-user-getUserBasicInfoBatchSize 50
              getUserBasicInfoTimeoutMs: &entity-user-getUserBasicInfoTimeoutMs 100
              stubs:
                getUserBasicInfo: &entity-user-stubs-getUserBasicInfo false
                getUserBasicInfoBatch: &entity-user-stubs-getUserBasicInfoBatch false
                getOaUsers: &entity-user-stubs-getOaUsers false
            toolingGatekeeper:
              url: &tooling-gatekeeper-url tooling-gatekeeper-grpc-api-headless.tooling.svc.cluster.local.:8080
              checkFeaturesBatchSize: &tooling-gatekeeper-checkFeaturesBatchSize 0
              checkFeaturesTimeoutMs: &tooling-gatekeeper-checkFeaturesTimeoutMs 10
              stubs:
                checkFeatures: &tooling-gatekeeper-stubs-checkFeatures ~
                getFeatures: &tooling-gatekeeper-stubs-getFeatures false
            toolingJobs:
              url: &tooling-jobs-go-url tooling-jobs-grpc-api-headless.tooling.svc.cluster.local:8080
            safetyCoreService:
              url: &safety-core-service-url safety-core-grpc-api-headless.safety.svc.cluster.local.:8080
            chatModerationService:
              url: &chat-moderation-service-url chat-moderation-grpc-api.chat:8080
            relationshipGraph:
              url: &relationship-graph-url relationship-graph-grpc-api-headless.relationship-graph.svc.cluster.local.:8080
              relationship-friend-relGraphDarkWritesPercentage: &relationship-friend-relGraphDarkWritesPercentage 100
              relationship-friend-relGraphDarkReadsPercentage: &relationship-friend-relGraphDarkReadsPercentage 100
              relationship-friend-relGraphSwitchReadsPercentage: &relationship-friend-relGraphSwitchReadsPercentage 100
            officialaccountsBackend:
              url: &officialaccounts-backend-url officialaccounts-backend-grpc-api-headless.officialaccounts.svc.cluster.local:8080
            entityPostService:
              url: &entity-post-service-url entity-post-grpc-api-headless.entity-post.svc.cluster.local:8080
              read-tags-from-entity-post-percent: &read-tags-from-entity-post-percent 100
            entityTopicService:
              url: &entity-topic-service-url entity-topic-grpc-api-headless.entity.svc.cluster.local:8080
            entityEventService:
              url: &entity-event-service-url entity-event-grpc-api-headless.entity.svc.cluster.local:8080
            eventBackendService:
              url: &event-backend-service-url event-backend-grpc-api-headless.event.svc.cluster.local:8080
            eventFrontendService:
              url: &event-frontend-service-url event-frontend-grpc-api.event.svc.cluster.local:8080
      
        safety:
          access-service: &default-safety-access-service
            vipUserIds:
              - PCWh8IuEJPinwi6BBfO0h #josepedronunes
      
        amplitude:
          analytics-key: &amplitude-analytics-key sm://backend-core-amplitude-experiment-api-key
          deployment-key: &amplitude-deployment-key sm://backend-core-recap-notification-deployment-key
      
        database: &default-database
          type: postgres
          synchronize: false
          skipMigrations: false
          logging: false
          host: pgbouncer-master.bereal-main.svc.cluster.local.
          port: 5432
          database: bereal
          username: bereal
          password: sm://backend-core-sql-master-bereal
          connectTimeoutMS: 10000
          extra:
            min: 0
            max: 10
            statement_timeout: 10000
            query_timeout: 15000
      
        database-2: &database-2
          type: postgres
          synchronize: false
          skipMigrations: false
          logging: false
          host: pgbouncer-master.bereal-main.svc.cluster.local.
          port: 5432
          database: bereal
          username: bereal
          password: sm://backend-core-sql-master-bereal
          extra:
            min: 0
            max: 10
            statement_timeout: 10000
            query_timeout: 15000
      
        redis: &default-redis
          mode: cluster
          lazyConnect: false
          enableOfflineQueue: true
          scaleReads: all
          nodes:
            - host: redis-cluster.redis-default-a.svc.cluster.local.
              port: 6379
          enableAutoPipelining: false
      
        redis-cluster: &default-redis-cluster
          mode: cluster
          lazyConnect: false
          enableOfflineQueue: true
          scaleReads: all
          nodes:
            - host: redis-cluster.redis-default-a.svc.cluster.local.
              port: 6379
          enableAutoPipelining: false
      
        entity:
          entity-user-spanner: &entity-user-spanner
            projectId: "backend-core-dev"
            instanceId: "entity-user"
            databaseId: "entity-user"
          entity-user-init-spanner: &entity-user-init-spanner false
      
        persistent-posts-spanner: &persistent-posts-spanner
          projectId: backend-core-dev
          instanceId: persistent-posts
      
        feature-store-spanner: &feature-store-spanner
          projectId: backend-core-dev
          instanceId: feature-store
      
        cache: &default-cache
          type: "redis"
          redis: *default-redis
      
        metrics: &default-metrics
          microTaskQueueProbe: false
          libuvProbe: false
          requestsInFlight: true
      
        person-recently-user-suspension: &default-person-recently-user-suspension
          enabled: true
          refreshIntervalSec: 60
          durationMin: 120
      
        analytics:
          analytics-database: &analytics-database
            <<: *default-database
            connectionName: analytics
      
        archive:
          archive-post-database: &archive-post-database
            <<: *default-database
            connectionName: archive-post
          archive-post-database-streamable: &archive-post-database-streamable
            <<: *default-database
            connectionName: archive-post-streamable
          archive-post-spanner: &archive-post-spanner
            <<: *persistent-posts-spanner
            databaseId: persistent-posts
          archive-post-comments-spanner: &archive-post-comments-spanner
            <<: *persistent-posts-spanner
            databaseId: persistent-post-comments
          archive-post-realmojis-spanner: &archive-post-realmojis-spanner
            <<: *persistent-posts-spanner
            databaseId: persistent-post-realmoji
          archive-post-tags-spanner: &archive-post-tags-spanner
            <<: *persistent-posts-spanner
            databaseId: persistent-post-tags
          spanner-comments-write-percentage: &spanner-comments-write-percentage 100
          post-service:
            shouldForceIndex: &should-force-index true
      
        memories-recap:
          memories-recap-spanner: &memories-recap-spanner
            projectId: backend-core-dev
            instanceId: "memories-recap"
            databaseId: "memories-recap"
          cache-memories-recap: &cache-memories-recap
            <<: *default-cache
            name: cache-memories-recap
      
        chat:
          redis-chat: &redis-chat
            <<: *default-redis
            connectionName: redis-chat
          cache-chat: &cache-chat
            name: cache-chat
            <<: *default-cache
            redis: *redis-chat
      
        auth:
          auth-token-spanner: &auth-token-spanner
            projectId: backend-core-dev
            instanceId: auth
            databaseId: "auth-refresh-token"
          redis-auth: &redis-auth
            <<: *default-redis
            connectionName: redis-auth
          cache-auth: &cache-auth
            name: cache-auth
            <<: *default-cache
            redis: *redis-auth
          session: &auth-session
            redis: *redis-auth
            cookieName: "bereal-auth"
            cookieSecret: sm://backend-core-auth-session-cookie-secret
            sessionPrefix: auth:session
          database-auth: &auth-database
            <<: *default-database
            connectionName: database-auth
          database-auth-streamable: &auth-database-streamable
            <<: *default-database
            connectionName: database-auth-streamable
          google:
            clientId: &auth-google-client-id ************-iuh8m8tklh21tp6oajh9llaq3booaflm.apps.googleusercontent.com
            clientSecret: &auth-google-client-secret sm://backend-core-auth-google-client-secret
      
          signin: &auth-signin
            publishCheckCodeTopic: true
            highRiskCountries:
              - UZ
              - RU
              - UA
              - BY
              - RO
              - KH
              - PK
              - VN
            phoneNumbersToAuthBySlack:
              - "+447741689623" #UK number
              - "+16174589232" #US number
              - "+33614986973" #Florent Champigny test 
              - "+33712345678" #Florent Champigny test 2
              - "+33798765432" #Gautier test 
              - "+33695012918" #Victor test
              - "+447935803277" #Adam test
              - "+33649638276" #Florent test 3
              - "+33779679379" #Alexandre test 1
              - "+61474772513" #Alexandre test 2
              - "+33645908728" #Fred test 1
              - "+33634636400" #Elliot test
              - "+33629658379" #Gautier test 2
              - "+33613943681" #Florian Ganzin test 1
              - "+447759844444" #Florian Ganzin test 2
              - "+447759855555" #Fred test 2
              - "+33610000000" #Charles Test
              - "+33610000001" #Cesar Test
              - "+33610000002" #Olive test
              - "+33610000003" #Aleksei Test
              - "+33610000004" #Botan Test
              - "+33610000005" #Chalom test
              - "+33610000006" #Chneor Test
              - "+33610000007" #Florent Bertrand Test
              - "+33610000008" #Jérémie Test
              - "+33610000009" #Kevin Ma test
              - "+33610000010" #Kevin Mo test
              - "+33610000011" #Lahssen test
              - "+33610000012" #Maximilien test
              - "+33610000013" #Michael Ustinov test
              - "+33610000014" #Michael Andrianarimanga test
              - "+33610000015" #Alexandre Jais
              - "+33610000016" #Clara test
              - "+33610000017" #Quang test
              - "+33610000018" #Aymeric test
              - "+33610000019" #Florian Chapuis test
              - "+33610000020" #Gaëtan test
              - "+33610000021" #William test
              - "+33610000022" #Nicolas test
              - "+33610000023" #Dov test
              - "+33610000024" #Onboarding test 
              - "+33610000025" #SRE team test
            providers:
              static:
                restricted: true
                weight: 0
                phoneNumbers:
                  - phoneNumber: "+16505551234" # apple store review
                    code: "138468" # https://xkcd.com/221/
                  - phoneNumber: "+33690000000" # QA automation (@Karl & @Vlad)
                    code: "506097"
                  - phoneNumber: "+33690000001" # QA automation (@Karl & @Vlad)
                    code: "506097"
                  - phoneNumber: "+33690000002" # QA automation (@Karl & @Vlad)
                    code: "506097"
                  - phoneNumber: "+33690000003" # QA automation (@Karl & @Vlad)
                    code: "506097"
                  - phoneNumber: "+33690000004" # QA automation (@Karl & @Vlad)
                    code: "506097"
                  - phoneNumber: "+33690000005" # QA automation (@Karl & @Vlad)
                    code: "506097"
                  - phoneNumber: "+33690000006" # QA automation (@Karl & @Vlad)
                    code: "506097"
                  - phoneNumber: "+33690000007" # QA automation (@Karl & @Vlad)
                    code: "506097"
                  - phoneNumber: "+33690000009" # QA automation (@Karl & @Vlad)
                    code: "506097"
                  - phoneNumber: "+33690000010" # QA automation (@Karl & @Vlad)
                    code: "506097"
                  - phoneNumber: "+33690000011" # QA automation (@Karl & @Vlad)
                    code: "506097"
                  - phoneNumber: "+33690000012" # QA automation (@Karl & @Vlad)
                    code: "506097"
                  - phoneNumber: "+33690000013" # QA automation (@Karl & @Vlad)
                    code: "506097"
                  - phoneNumber: "+33690000014" # QA automation (@Karl & @Vlad)
                    code: "506097"
                  - phoneNumber: "+33690000015" # QA automation (@Karl & @Vlad)
                    code: "506097"
                  - phoneNumber: "+33690000016" # QA automation (@Karl & @Vlad)
                    code: "506097"
                  - phoneNumber: "+33690000017" # QA automation (@Karl & @Vlad)
                    code: "506097"
                  - phoneNumber: "+33690000018" # QA automation (@Karl & @Vlad)
                    code: "506097"
                  - phoneNumber: "+33690000019" # QA automation (@Karl & @Vlad)
                    code: "506097"
                  - phoneNumber: "+33690000020" # QA automation (@Karl & @Vlad)
                    code: "506097"
              slack:
                restricted: true
                weight: 0
                codeLength: 6
                codeValiditySeconds: 300
                slack:
                  channel: T07ELDMJ9/B090XTYP8J2/hXBFx1KJYdFOeesvTREtjQxH # ask SRE to renew https://api.slack.com/apps/A07DTEK7FCP/general
              vonage:
                restricted: false
                apiKey: "2e0ad396" #default
                apiSecret: sm://backend-core-auth-vonage-api-key
                signinApiKey: "2e0ad396"
                signinApiSecret: sm://backend-core-auth-vonage-api-key
                signupHighRiskCountriesApiKey: "2e0ad396"
                signupHighRiskCountriesApiSecret: sm://backend-core-auth-vonage-api-key
                signupLowRiskCountriesApiKey: "2e0ad396"
                signupLowRiskCountriesApiSecret: sm://backend-core-auth-vonage-api-key
                weight: 50
              ding:
                restricted: false
                customerUuid: 483dbcc0-83c0-4704-9ca6-1cd2a2b3e2d5
                secretToken: sm://backend-core-auth-ding-api-key
                weight: 50
                supportedCountries:
                  - AD
                  - AE
                  - AF
                  - AG
                  - AI
                  - AL
                  - AO
                  - AR
                  - AS
                  - AT
                  - AU
                  - AW
                  - AZ
                  - BA
                  - BB
                  - BD
                  - BE
                  - BF
                  - BG
                  - BH
                  - BI
                  - BJ
                  - BM
                  - BN
                  - BO
                  - BR
                  - BS
                  - BT
                  - BW
                  - BZ
                  - CA
                  - CD
                  - CF
                  - CG
                  - CH
                  - CI
                  - CK
                  - CL
                  - CN
                  - CO
                  - CR
                  - CV
                  - CW
                  - CY
                  - CY
                  - CZ
                  - DE
                  - DJ
                  - DK
                  - DM
                  - DO
                  - DZ
                  - EC
                  - EE
                  - EG
                  - ER
                  - ES
                  - FI
                  - FJ
                  - FK
                  - FM
                  - FO
                  - FR
                  - GA
                  - GB
                  - GD
                  - GE
                  - GF
                  - GG
                  - GI
                  - GL
                  - GM
                  - GP
                  - GQ
                  - GR
                  - GT
                  - GU
                  - GY
                  - HK
                  - HN
                  - HR
                  - HT
                  - HU
                  - ID
                  - IE
                  - IL
                  - IM
                  - IN
                  - IQ
                  - IS
                  - IT
                  - JE
                  - JM
                  - JP
                  - KG
                  - KH
                  - KI
                  - KM
                  - KN
                  - KR
                  - KW
                  - KY
                  - LA
                  - LB
                  - LC
                  - LI
                  - LK
                  - LS
                  - LT
                  - LU
                  - LV
                  - LY
                  - MA
                  - MC
                  - MD
                  - ME
                  - MG
                  - MK
                  - ML
                  - MM
                  - MN
                  - MO
                  - MQ
                  - MR
                  - MS
                  - MT
                  - MU
                  - MV
                  - MW
                  - MX
                  - MY
                  - MZ
                  - NA
                  - NC
                  - NE
                  - NF
                  - NI
                  - NL
                  - NO
                  - NP
                  - NR
                  - NZ
                  - OM
                  - PA
                  - PE
                  - PF
                  - PG
                  - PH
                  - PL
                  - PM
                  - PR
                  - PS
                  - PT
                  - PW
                  - PY
                  - QA
                  - RE
                  - RO
                  - RU
                  - SA
                  - SB
                  - SC
                  - SE
                  - SG
                  - SI
                  - SK
                  - SL
                  - SM
                  - SN
                  - SO
                  - SR
                  - ST
                  - SV
                  - SY
                  - TC
                  - TD
                  - TG
                  - TH
                  - TJ
                  - TL
                  - TM
                  - TN
                  - TO
                  - TR
                  - TT
                  - TW
                  - UA
                  - US
                  - UY
                  - UZ
                  - VC
                  - VE
                  - VG
                  - VN
                  - VU
                  - WF
                  - WS
                  - XK
                  - YE
                  - ZA
                  - ZW
              boomware:
                restricted: false
                weight: 0
                username: "03143db910f9d67a3f60fec4277c736e"
                password: "171581d9"
                supportedCountries:
                  - AZ
            throttling:
              ip:
                requests: 100
                window: 60
              phonenumber:
                requests: 5
                window: 60
              deviceId:
                requests: 5
                window: 60
              checkCode:
                requests: 5
                window: 60
            firebaseTokenKey: sm://backend-core-auth-firebase-verify-key
          clients:
            &auth-clients # uuidgen | tr '[:lower:]' '[:upper:]' to generate a new key
            - client_id: android
              client_secret: 9934E24B-06AB-4C8B-AC91-20D7E9DE355F
              life_time: 600
              grant_types:
                - firebase
                - firebase_2fa
                - refresh_token
                - phone
            - client_id: ios
              client_secret: 25B73F5D-5496-499D-A93A-253D6FBCFE32
              life_time: 600
              grant_types:
                - firebase
                - firebase_2fa
                - refresh_token
                - phone
            - client_id: impersonation
              client_secret: 8EA8F5AB-46EB-4BF8-8A3B-C903452EA7FC
              life_time: 600
              grant_types:
                - phone
            - client_id: moment-ui
              client_secret: 91E9DD7C-AFC8-44B6-8A20-74F6CE3BA823
              life_time: 3600
              redirect_urls:
                - http://localhost
                - https://tools.dev.bereal.team/main/moment
              scopes:
                - regions
                - moments
                - moments:admin
              grant_types:
                - authorization_code
                - refresh_token
      
            - client_id: regions-ui
              client_secret: F3865399-D72D-47FD-A001-4707B957B8BE
              life_time: 3600
              redirect_urls:
                - http://localhost
                - https://tools.dev.bereal.team/main/regions
              scopes:
                - regions
              grant_types:
                - authorization_code
                - refresh_token
      
            - client_id: deprecated-friendship-ui
              client_secret: 7F0F6DD2-1CE6-4554-9CCD-5533BDE0ABCC
              life_time: 3600
              redirect_urls:
                - http://localhost
                - https://tools.dev.bereal.team/main/deprecated-friendship
              scopes:
                - regions
              grant_types:
                - authorization_code
                - refresh_token
      
            - client_id: profile-ui
              client_secret: 17F8C888-B133-4B90-88E4-B4784A539D41
              life_time: 3600
              redirect_urls:
                - http://localhost
                - https://tools.dev.bereal.team/main/profile
              scopes:
                - profile
                - user:admin
              grant_types:
                - authorization_code
                - refresh_token
            - client_id: ambassador-ui
              client_secret: D0477A12-132E-44C9-BA60-4EDCDC18AEA4
              life_time: 3600
              redirect_urls:
                - http://localhost
                - https://tools.dev.bereal.team/main/ambassadors
              scopes:
                - ambassador
                - ambassador:admin
              grant_types:
                - authorization_code
                - refresh_token
            - client_id: user-roles-ui
              client_secret: F3E73D11-32B2-49D5-9500-EB3703304663
              life_time: 3600
              redirect_urls:
                - http://localhost
                - https://tools.dev.bereal.team/main
              scopes:
                - user:admin
              grant_types:
                - authorization_code
                - refresh_token
            - client_id: redis-ui
              client_secret: 7583e989-99b1-4d9a-8acb-1a53f187cba2
              life_time: 3600
              redirect_urls:
                - http://localhost
                - https://tools.dev.bereal.team/main/redis
              scopes:
                - redis
              grant_types:
                - authorization_code
                - refresh_token
            - client_id: doc-ui
              client_secret: 6C1F0D48-C335-43EA-9A81-5A7B240098E8
              life_time: 3600
              redirect_urls:
                - http://localhost
                - https://dev.doc.bereal.team/main
              scopes:
                - doc
              grant_types:
                - authorization_code
                - refresh_token
            - client_id: me-ui
              client_secret: 1DD86D32-57BE-4CDE-AF22-E4F563AD92E0
              life_time: 86400
              redirect_urls:
                - http://localhost
                - https://tools.dev.bereal.team/main/me
              scopes:
                - ambassador
                - ambassador:admin
                - moderation
                - regions
                - moments
                - moments:admin
                - profile
                - user:admin
                - doc
                - tooling-jobs
                - fof
                - settings
                - gatekeeper
              grant_types:
                - authorization_code
                - refresh_token
            - client_id: tooling-job-runner-ui
              client_secret: ED41F891-A421-45BA-9833-EAB870A0E7F7
              life_time: 3600
              redirect_urls:
                - http://localhost
                - https://tools.dev.bereal.team/main/jobs
              scopes:
                - tooling-jobs
              grant_types:
                - authorization_code
                - refresh_token
            - client_id: auth-roles-ui
              client_secret: 791E5D52-20DB-4680-8139-284B54D71926
              life_time: 3600
              redirect_urls:
                - http://localhost
                - https://tools.dev.bereal.team/main/roles
              scopes:
                - auth
              grant_types:
                - authorization_code
                - refresh_token
            - client_id: postman
              client_secret: 75C76814-1793-4837-9902-DB44AE30582B
              life_time: 3600
              firebase_token: true
              redirect_urls:
                - https://oauth.pstmn.io/v1
              scopes:
                - profile
                - moderation
              grant_types:
                - client_credentials
                - authorization_code
                - refresh_token
                - firebase
                - firebase_2fa
                - phone
            - client_id: fof-ui
              client_secret: 504B5867-29F4-4D8B-9938-492840ADA4F1
              life_time: 3600
              redirect_urls:
                - http://localhost
                - https://tools.dev.bereal.team/main
              scopes:
                - fof
              grant_types:
                - authorization_code
                - refresh_token
            - client_id: settings-ui
              client_secret: C5197277-BF59-4592-A12A-249BDABF30C5
              life_time: 3600
              redirect_urls:
                - http://localhost
                - https://tools.dev.bereal.team/main
              scopes:
                - settings
              grant_types:
                - authorization_code
                - refresh_token
            - client_id: gatekeeper-ui
              client_secret: 51BF679C-B7EF-4BA8-9F5A-9C3CA7600631
              life_time: 3600
              redirect_urls:
                - http://localhost
                - https://tools.dev.bereal.team/main
              scopes:
                - gatekeeper
              grant_types:
                - authorization_code
                - refresh_token
            - client_id: keda-scaler-ui
              client_secret: 90B87C69-892D-4798-A415-4596BCD2B4A4
              life_time: 3600
              redirect_urls:
                - http://localhost
                - https://tools.dev.bereal.team/main
              scopes:
                - settings
              grant_types:
                - authorization_code
                - refresh_token
            - client_id: bereal-web-ui
              client_secret: 4756DC8F-DCCD-4325-B50C-121F030415B9
              life_time: 3600
              redirect_urls:
                - http://localhost
                - https://tools.dev.bereal.team/main/bereal-web
              scopes:
                - bereal-web
                - bereal-web:impersonate
              grant_types:
                - authorization_code
                - refresh_token
            - client_id: auth-user-ui
              client_secret: E8B70A99-0E21-42DE-A125-FBB1D8FEF8A8
              life_time: 3600
              redirect_urls:
                - http://localhost
                - https://tools.dev.bereal.team/main/user
              scopes:
                - auth-user
              grant_types:
                - authorization_code
                - refresh_token
            - client_id: inapp-messages-segments-loader-ui
              client_secret: 1F1FCD0B-97A4-43F3-A981-4F0ECB882B23
              life_time: 3600
              redirect_urls:
                - http://localhost
                - https://tools.dev.bereal.team/main/segments-loader
              scopes:
                - inapp-messages
              grant_types:
                - authorization_code
                - refresh_token
            - client_id: tooling-qa-ui
              client_secret: 08236f64-7cac-4740-8fd2-4d9713a1074c
              life_time: 3600
              redirect_urls:
                - http://localhost
                - https://tools.dev.bereal.team/main/qa
              scopes:
                - qa
              grant_types:
                - authorization_code
                - refresh_token
            - client_id: profile-ui-v2
              client_secret: 105bcb3d-14a9-4356-a28d-e845ae145de2
              life_time: 3600
              redirect_urls:
                - http://localhost
                - https://tools.dev.bereal.team/main/profiles
              scopes:
                - profile
              grant_types:
                - authorization_code
                - refresh_token
      
        bereal:
          database-moments: &database-moments
            <<: *default-database
            connectionName: database-moment
          redis-moment: &redis-moment
            <<: *default-redis
            connectionName: redis-moment
          cache-moment: &cache-moment
            name: cache-moment
            <<: *default-cache
            redis: *redis-moment
          rtdb-urls: &moment-rtdb-urls
            - "https://bereal-notif-1.europe-west1.firebasedatabase.app/"
            - "https://bereal-notif-2.europe-west1.firebasedatabase.app/"
            - "https://bereal-notif-3.europe-west1.firebasedatabase.app/"
            - "https://bereal-notif-4.europe-west1.firebasedatabase.app/"
            - "https://bereal-notif-5.europe-west1.firebasedatabase.app/"
          region: &bereal-region
            regions:
              - code: us-central
                enableAutoAssign: true
                timezone: "America/Chicago"
                countries: ["GS"]
                hoursCoefs: [0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 8, 9, 10, 12, 12, 10, 9, 8, 8, 8, 3, 0, 0, 0]
                name:
                  en: Americas
                  fr: Amériques
                  es: América
                  de: Amerika
                  zh_CN: 美洲
                  ko: 북/남미
                  ja: アメリカ
                  nl: Amerika
                  pt: Americas
                  it: Americhe
              - code: europe-west
                enableAutoAssign: true
                timezone: "Europe/Paris"
                countries: ["CV"]
                hoursCoefs: [0, 0, 0, 0, 0, 0, 0, 0, 5, 8, 11, 11, 2, 5, 11, 11, 11, 11, 10, 9, 3, 0, 0, 0]
                name:
                  en: Europe
                  fr: Europe
                  es: Europa
                  de: Europa
                  zh_CN: 欧洲
                  ko: 유럽
                  ja: ヨーロッパ
                  nl: Europa
                  pt: Europa
                  it: Europa
              - code: asia-west
                enableAutoAssign: true
                timezone: "Asia/Karachi"
                hoursCoefs: [0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 6, 9, 9, 10, 10, 9, 9, 9, 9, 9, 6, 2, 0, 0]
                name:
                  en: West Asia
                  fr: Asie de l'Ouest
                  es: Oeste Asiático
                  de: Westasien
                  zh_CN: 西亚
                  ko: 서아시아
                  ja: 西アジア
                  nl: West Azië
                  pt: Ásia Ocidental
                  it: Ovest Asia
              - code: asia-east
                enableAutoAssign: true
                timezone: "Australia/Sydney"
                hoursCoefs: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 7, 9, 12, 11, 10, 10, 10, 10, 9, 6, 3, 0, 0]
                name:
                  en: East Asia
                  fr: Asie de l'Est
                  es: Asia del Este
                  de: Ostasien
                  zh_CN: 东亚
                  ko: 동아시아
                  ja: 東アジア
                  nl: Oost Azië
                  pt: Ásia Oriental
                  it: Est Asia
              - code: dev1
                enableAutoAssign: false
                timezone: "Europe/Paris"
                hoursCoefs: [0, 0, 0, 0, 0, 0, 0, 0, 5, 8, 11, 11, 2, 5, 11, 11, 11, 11, 10, 9, 3, 0, 0, 0]
                name:
                  en: Dev Region 1
      
        common:
          chat-stream: &common-chat-stream
            apiKey: sm://backend-core-chat-stream-api-key
            apiKeySecret: sm://backend-core-chat-stream-api-key-secret
      
        feeds:
          user-stats-final-redis: &user-stats-final-redis
            <<: *default-redis
            connectionName: user-stats-final-redis
          database-feeds: &database-feeds
            <<: *default-database
            connectionName: feeds
          redis-discovery: &redis-discovery
            <<: *default-redis
            connectionName: redis-discovery
          redis-memories: &redis-memories
            <<: *default-redis
            connectionName: redis-memories
          cache-memories: &cache-memories
            name: cache-memories
            <<: *default-cache
            redis: *redis-memories
          redis-memories-video: &redis-memories-video
            <<: *default-redis
            connectionName: redis-memories-video
          cache-memories-video: &cache-memories-video
            <<: *default-cache
            name: cache-memories-video
            redis: *redis-memories-video
          redis-feeds-friends: &redis-feeds-friends
            <<: *default-redis
            connectionName: redis-feeds-friends
          redis-feeds-friends-v1: &redis-feeds-friends-v1
            <<: *default-redis
            connectionName: redis-feeds-friends-v1
          cache-feeds-friends: &cache-feeds-friends
            name: cache-feeds-friends
            <<: *default-cache
            redis: *redis-feeds-friends
          redis-feeds-content: &redis-feeds-content
            <<: *default-redis
            connectionName: redis-feeds-content
          cache-feeds-content: &cache-feeds-content
            name: cache-feeds-content
            <<: *default-cache
            redis: *redis-feeds-content
          redis-feeds-fof: &redis-feeds-fof
            <<: *default-redis
            connectionName: redis-feeds-fof
          cache-feeds-fof-feeds: &cache-feeds-fof-feeds
            <<: *default-cache
            name: cache-feeds-fof-feeds
            redis: *redis-feeds-fof
          feeds-friends-database: &feeds-friends-database
            <<: *default-database
            connectionName: feeds-friends
      
        content:
          content-database: &content-database
            <<: *default-database
            connectionName: content
          content-database-streamable: &content-database-streamable
            <<: *default-database
            connectionName: content-stream
          content-post-database: &content-post-database
            <<: *database-2
            connectionName: content-post
          content-post-database-raw-connection: &content-post-database-raw-connection
            <<: *database-2
            connectionName: content-post-raw-connection
          content-comment-database: &content-comment-database
            <<: *database-2
            connectionName: content-comment
          content-realmoji-database: &content-realmoji-database
            <<: *database-2
            connectionName: content-realmoji
          content-screenshot-database: &content-screenshot-database
            <<: *database-2
            connectionName: content-screenshot
          content-tag-database: &content-tag-database
            <<: *database-2
            connectionName: content-tag
          content-tag-database-streamable: &content-tag-database-streamable
            <<: *database-2
            connectionName: content-tag-streamable
          redis-content: &redis-content
            <<: *default-redis
            connectionName: redis-content
          cache-content: &cache-content
            name: cache-content
            <<: *default-cache
            redis: *redis-content
          redis-content-realmoji-notification: &redis-content-realmoji-notification
            <<: *default-redis
            connectionName: redis-content-realmoji-notification
          cache-content-realmoji-notification: &cache-content-realmoji-notification
            <<: *default-cache
            name: cache-content-realmoji-notification
            redis: *redis-content-realmoji-notification
          post-service:
            maxPostsPerMoment: &maxPostsPerMoment 10
            entityPostReadPercentage: &entityPostReadPercentage 100
          recap-notification-experiment: &recap-notification-experiment
            deploymentKey: *amplitude-deployment-key
            analyticsKey: *amplitude-analytics-key
            experimentFlagKey: recap-notif-apr-2024
            subjectLineExperimentFlagKey: recap-notif-subject-line-optimization
            controlVariant: control
          recap-notification-default-params: &recap-notification-default-params
            minutesAfterMomentNotification: 30
            minutesAfterLastActivity: 60
            minutesAfterLastSent: 60
            maxRecapNotificationSent: 5
            minPostsToQualify: 1
            pushSettingsDefaultState: "true"
          recap-notification-experiment-use-gatekeeper: &recap-notification-experiment-use-gatekeeper false
          redis-content-recap-notification: &redis-content-recap-notification
            <<: *default-redis
            connectionName: redis-content-recap-notification
          cache-content-recap-notification: &cache-content-recap-notification
            <<: *default-cache
            name: cache-content-recap-notification
            redis: *redis-content-recap-notification
          recap-notification-scheduler-service-users-partition-count: &recap-notification-scheduler-service-users-partition-count 1
          recap-notification-scheduler-service-minutes-per-bucket: &recap-notification-scheduler-service-minutes-per-bucket 10
          content-streak-reminders-experiment: &content-streak-reminders-experiment
            deploymentKey: *amplitude-deployment-key
            analyticsKey: *amplitude-analytics-key
          redis-content-streak-reminders: &redis-content-streak-reminders
            <<: *redis-content
            connectionName: redis-content-streak-reminders
          content-streak-reminders-service-users-partition-count: &content-streak-reminders-service-users-partition-count 128
      
        notifications:
          notification-database-streamable: &notification-database-streamable
            <<: *default-database
            connectionName: notification
            host: db.master-new.bereal.lan
          notification-database: &notification-database
            <<: *default-database
            connectionName: notification
          redis-notification: &redis-notification
            <<: *default-redis
            connectionName: redis-notification
          cache-notification: &cache-notification
            name: cache-notification
            <<: *default-cache
            redis: *redis-notification
      
        person:
          redis-person: &redis-person
            <<: *default-redis
            connectionName: redis-person
          cache-person: &cache-person
            name: cache-person
            <<: *default-cache
            redis: *redis-person
          person-database: &person-database
            <<: *default-database
          person-database-streamable: &person-database-streamable
            <<: *person-database
          person-user-suspensions-database: &person-user-suspensions-database
            <<: *default-database
          person-user-state-spanner: &user-state-spanner
            projectId: backend-core-dev
            instanceId: feature-store
            databaseId: "user-labels"
          user-service:
            allowedNumberOfBirthdateUpdates: &allowedNumberOfBirthdateUpdates 10
            updateBirthdateIntervalInDays: &updateBirthdateIntervalInDays 1
            enableUserFreshness: &enableUserFreshness true
            userFreshnessCheckPct: &userFreshnessCheckPct 100
            entity-user-get-user-pct: &entity-user-get-user-pct 100
          user-suspension-service:
            recentlyUserSuspension: *default-person-recently-user-suspension
          collect-gender-pct: &collect-gender-pct 100
      
        moderation:
          database-moderation: &database-moderation
            <<: *default-database
          moderation-report-database: &moderation-report-database
            <<: *database-moderation
            connectionName: moderation-report
      
        radioactive:
          radioactive-database: &radioactive-database
            <<: *default-database
      
        officialaccounts:
          oa-list-service: &oa-list-service
            cacheRefreshIntervalSeconds: 10
      
        recommendations:
          recommendations-friends-database: &recommendations-friends-database
            <<: *default-database
          recommendations-friends-database-streamable:
            &recommendations-friends-database-streamable
            <<: *default-database
          redis-recommendations-friends: &redis-recommendations-friends
            <<: *default-redis
            connectionName: redis-recommendations-friends
          cache-recommendations-friends: &cache-recommendations-friends
            <<: *default-cache
            name: cache-recommendations-friends
            redis: *redis-recommendations-friends
          redis-recommendations-cache: &redis-recommendations-cache
            <<: *default-redis
            connectionName: redis-recommendations-cache
          cache-recommendations-cache: &cache-recommendations-cache
            <<: *default-cache
            redis: *redis-recommendations-cache
      
        ranking:
          friend-ranking-spanner: &friend-ranking-spanner
            <<: *feature-store-spanner
            databaseId: "friendship-ranking"
          applyRankingSchemas: true # only create them in local/dev. They're created externally in prod
          default-friend-ranking-alg: &default-friend-ranking-alg "sent_engagements_normalized"
          friend-ranking-tables: &friend-ranking-tables
            - tableName: "friend_rank_sent_engagements"
              rankingAlg: "sent_engagements"
            - tableName: "friend_rank_mutual_engagements"
              rankingAlg: "mutual_engagements"
            - tableName: "friend_rank_sent_engagements_normalized"
              rankingAlg: "sent_engagements_normalized"
            - tableName: "friend_rank_mutual_engagements_normalized"
              rankingAlg: "mutual_engagements_normalized"
      
        relationship:
          rel-graph-hidden-read-base-pct: &rel-graph-hidden-read-base-pct 1
          relationship-graph-friends-write: &relationship-graph-friends-write true
          relationship-graph-friends-read-pct: &relationship-graph-friends-read-pct 1
          entity-user-friends-user-read-pct: &entity-user-friends-user-read-pct 1
          save-friends-deletes: &save-friends-deletes false
          relationship-graph-friends-double-read-pct: &relationship-graph-friends-double-read-pct 0
      
          relationship-database: &relationship-database
            <<: *default-database
          relationship-database-streamable: &relationship-database-streamable
            <<: *default-database
      
          redis-relationship-contacts: &redis-relationship-contacts
            <<: *default-redis
            connectionName: redis-relationship-contacts
      
          redis-relationship-friends: &redis-relationship-friends
            <<: *default-redis
            connectionName: redis-relationship-friends
      
          redis-relationship-users: &redis-relationship-users
            <<: *default-redis
            connectionName: redis-relationship-users
      
          redis-relationship-suggestions: &redis-relationship-suggestions
            <<: *default-redis
            connectionName: redis-relationship-suggestions
      
        music:
          spotify: &music-spotify
            clientId: sm://backend-core-spotify-client-id
            clientSecret: sm://backend-core-spotify-client-secret
            spotifyTokenApiUrl: https://accounts.spotify.com/api/token
            spotifyAuthorizeUrl: https://accounts.spotify.com/authorize
            oauthCallbackUrl: https://mobile-l7.dev.bereal.com/main/api/music/spotify/token
            appRedirectUri: bere.al://auth/spotify-login-callback
      
          redis: &redis-music
            <<: *default-redis
            connectionName: redis-music
      
        partner:
          database-partner: &database-partner
            <<: *default-database
          partner-zendesk-database: &partner-zendesk-database
            <<: *database-partner
            connectionName: partner-zendesk
      
        search:
          search-database: &search-database
            <<: *default-database
          redis-search: &redis-search
            <<: *default-redis
            connectionName: redis-search
          cache-search: &cache-search
            name: cache-search
            <<: *default-cache
            redis: *redis-search
      
        settings:
          settings-database: &settings-database
            <<: *default-database
          settings-database-streamable: &settings-database-streamable
            <<: *default-database
          redis-settings: &redis-settings
            <<: *default-redis
            connectionName: redis-settings
          cache-settings: &cache-settings
            name: cache-settings
            <<: *default-cache
            redis: *redis-settings
      
        terms:
          redis-terms: &redis-terms
            <<: *default-redis
            connectionName: redis-terms
          cache-terms: &cache-terms
            name: cache-terms
            <<: *default-cache
            redis: *redis-terms
      
        tooling:
          redis-tooling: &redis-tooling
            <<: *default-redis
            connectionName: redis-tooling
          cache-tooling: &cache-tooling
            name: cache-tooling
            <<: *default-cache
            redis: *redis-tooling
      
          redis-throttling: &redis-throttling
            <<: *default-redis
            connectionName: redis-throttling
          redis-session: &redis-session
            <<: *default-redis
            connectionName: redis-session
          database-tooling: &database-tooling
            <<: *default-database
          tooling-activity-log-database: &tooling-activity-log-database
            <<: *default-database
            connectionName: tooling-activity-log
      
          gatekeeper-enablePgFallback: &tooling-gatekeeper-enablePgFallback true
          gatekeeper-features: &tooling-gatekeeper-features
            - name: friendsOfFriendsFeed
              activated: true
            - name: music
              activated: true
            - name: musicProviderSpotify
              activated: true
            - name: musicProviderApple
              activated: true
            - name: music
              activated: true
            - name: musicProviderSpotify
              activated: true
            - name: musicProviderApple
              activated: true
            - name: veeOne
              activated: true
            - name: discoveryFeed
              activated: false
          just-words-service:
            orgId: &just-words-service-org-id sm://backend-core-just-words-service-org-id
            sdkKey: &just-words-service-sdk-key sm://backend-core-just-words-service-sdk-key
      
        serviceAccount: &default-service-account
          projectId: "backend-core-dev"
      
        recaptcha-service-account: &recaptcha-service-account
          projectId: backend-core-dev
      
        firebase-account: &default-firebase-account
          projectId: "bereal-sample"
          credentials:
            client_email: "<EMAIL>"
            private_key: sm://backend-core-sa-fasterstore
      
        firebase: &default-firebase
          serviceAccount: *default-firebase-account
      
        auth-service:
          jwtSecret: &default-jwt-secret sm://backend-core-auth-jwt-key
          authEndpoint: &default-oauth-auth-endpoint https://dev.auth.bereal.team/main/auth
          tokenEndpoint: &default-oauth-token-endpoint https://dev-auth.bereal.com/main/token
      
        authentication: &default-authentication
          firebase:
            serviceAccount: *default-firebase-account
          jwtSecrets:
            - *default-jwt-secret
          userValidationEnabled: true
      
        oauth-client: &default-oauth-client
          securedPath:
            - "*"
          authorizeUri: *default-oauth-auth-endpoint
          tokenUri: *default-oauth-token-endpoint
      
        webapp-session: &default-webapp-session
          redis: *redis-session
      
        throttling: &default-throttling
          redis: *redis-throttling
          enable: false
          ip:
            max: 10000
            ttl: 60
          user:
            max: 6000
            ttl: 60
      
        request-signature: &request-signature
          enabled: false
          secret: "sm://backend-core-common-api-request-signature-secret"
          enforce: false
          exclude:
            - "/status"
            - "/version"
            - "/metrics"
            - "/configurations"
      
        app: &default-app
          host: 0.0.0.0
          port: 8080
          name: bereal-app
          apiPrefix: api
          context: true
          security: true
          health: true
          logger: *default-logger
          requestSignature: *request-signature
          throttling: *default-throttling
          metrics: *default-metrics
          concurrency:
            checkInterval: 500
          readiness:
            notReady: 300
            ready: 100
            rejectRequests: false
          prometheus:
            enable: true
            expose: true
          keepAliveTimeout: 300000
          headersTimeout: 60000
          gracePeriodMs: 5000
      
        grpc-app: &default-grpc-app
          host: 0.0.0.0
          port: 8080
          technicalPort: 3001
          logger: *default-logger
          gracePeriodMs: 5000
          type: "grpc"
      
        secret-manager: &default-secret-manager
          serviceAccount: *default-service-account
          defaultPrefix: "projects/************/secrets/"
      
        pub: &default-pub
          redis: *redis-tooling
          consume: false
          serviceAccount: *default-service-account
      
        sub: &default-sub
          redis: *redis-tooling
          consume: true
          serviceAccount: *default-service-account
      
        serviceAccountUpload: &service-account-upload
          projectId: "backend-core-dev"
          credentials:
            client_email: "<EMAIL>"
            private_key: sm://backend-core-sa-fasterstore
      
        default-bereal-bucket: &default-bereal-bucket
          name: &default-bucket-name sandbox-storage.bere.al
          serviceAccount: *service-account-upload
          useLegacyAcl: true
      
        us1-bucket: &us1-bucket
          name: &us1-bucket-name us1-sandbox-storage.bere.al
          serviceAccount: *service-account-upload
      
        default-memories-bucket: &default-memories-bucket
          name: *default-bucket-name
          serviceAccount: *default-service-account
      
        memories-video-bucket: &memories-video-bucket
          name: *default-bucket-name
          serviceAccount: *default-service-account
      
        default-content-post-bucket: &default-content-post-bucket
          name: *default-bucket-name
          serviceAccount: *service-account-upload
      
        default-content-post-storage: &default-content-post-storage
          name: content-post
          selector:
            method: direct
            bucket: *default-content-post-bucket
      
        default-content-realmoji-bucket: &default-content-realmoji-bucket
          name: *default-bucket-name
          serviceAccount: *service-account-upload
      
        default-content-realmoji-storage: &default-content-realmoji-storage
          name: content-realmoji
          selector:
            method: direct
            bucket: *default-content-realmoji-bucket
      
        default-person-realmoji-bucket: &default-person-realmoji-bucket
          name: *default-bucket-name
          serviceAccount: *service-account-upload
      
        default-person-realmoji-storage: &default-person-realmoji-storage
          name: person-realmoji
          selector:
            method: direct
            bucket: *default-person-realmoji-bucket
      
        default-person-bucket: &default-person-bucket
          name: *default-bucket-name
          serviceAccount: *service-account-upload
      
        default-person-storage: &default-person-storage
          name: person
          selector:
            method: direct
            bucket: *default-person-bucket
      
        default-arbitrary-storage: &default-arbitrary-storage
          name: arbitrary
          selector:
            method: arbitrary
            serviceAccount: *default-service-account
      
        default-benchmark-storage: &default-benchmark-storage
          name: benchmark
          selector:
            method: direct
            bucket: *default-bereal-bucket
      
        multibucket-storage: &multibucket-storage
          name: multibucket-storage
          selector:
            method: multibucket
            defaultBucket: *us1-bucket
            fallbackBucket: *default-bereal-bucket
            buckets:
              - *default-bereal-bucket
              - *us1-bucket
      
        # multibucket config here because
        #   in 2021 we uploaded the memories video on the default bereal bucket
        #   in 2022 we uploaded the memories video on a dedicated bucket
        memories-video-multibucket-storage: &memories-video-multibucket-storage
          name: memories-video-multibucket-storage
          selector:
            method: multibucket
            defaultBucket: *default-bereal-bucket
            fallbackBucket: *default-bereal-bucket
            buckets:
              - *default-bereal-bucket
              - *memories-video-bucket
      
        default-bereal-storage: &default-bereal-storage
          name: bereal
          selector:
            method: direct
            bucket: *default-bereal-bucket
      
        default-memories-storage: &default-memories-storage
          name: memories
          selector:
            method: direct
            bucket: *default-memories-bucket
      
        memories-video-storage: &memories-video-storage
          name: memories-video
          selector:
            method: direct
            bucket: *memories-video-bucket
      
        gdpr-bucket: &gdpr-bucket
          name: &gdpr-bucket-name bereal-gdpr-bucket
          serviceAccount: *default-service-account
      
        gdpr-storage: &gdpr-storage
          name: gdpr
          selector:
            method: direct
            bucket: *default-bereal-bucket
      
        chat-media-bucket: &chat-media-bucket
          name: &chat-media-bucket-name bereal-us-central1-dev-chat-media
          serviceAccount: *default-service-account
      
        chat-media-storage: &chat-media-storage
          name: chat-media
          selector:
            method: direct
            bucket: *chat-media-bucket
      
        default-storagePresenter: &default-storage-presenter
          logger: *default-logger
          defaultBucket:
            name: sandbox-storage.bere.al
            url: "https://dev-cdn.bereal.network/"
          buckets:
            - name: bereal-gdpr-bucket
              url: https://dev-gdpr.bere.al/
            - name: sandbox-storage.bere.al
              resizedUrl: https://dev-cdn-resize.bereal.network/
              processor: cloudflare
              cdn:
                - url: https://dev-cdn.bereal.network/
                  weight: 50
                - url: https://dev-cdn-resize.bereal.network/
                  weight: 50
            - name: us1-sandbox-storage.bere.al
              resizedUrl: https://dev-cdn-us1.bereal.network/
              cdn:
                - url: https://dev-cdn-us1.bereal.network/
                  weight: 50
                - url: https://cdn-mc-us1-b7f1994b.dev.bereal.network/
                  weight: 50
            - name: us1-storage.bere.al
              url: https://cdn-us1.bereal.network/
      
      
        default-notification-push-bucket: &default-notification-push-bucket
          name: *default-bucket-name
          serviceAccount: *default-service-account
      
        default-notification-push-storage: &default-notification-push-storage
          name: notification-push
          selector:
            method: direct
            bucket: *default-notification-push-bucket
      
        job: &default-job
          redis: *redis-tooling
          pubSub: *default-pub
          logger: *default-logger
          database: &job-database
            <<: *default-database
      
        default-realpush: &default-realpush
          token: &default-realpush-token sm://backend-core-realpush-token
      
        maxPostDeletion: &max-post-deletion 10
      
        default-chat:
          moderation-service: &default-chat-moderation-service
            apiUrl: chat-moderation-grpc-api.chat:8080
      
        new-memories-path-users: &new-memories-path-users
          enabledUserIds:
            - jOB17j2A8MvV33U3bJcwU # kyle
          defaultState: "disabled"
      
      datadog-tracer:
        enabled: true
        logInjection: true
        runtimeMetrics: true
        profiling: false
      
      secret-manager: *default-secret-manager
      
      validator:
        whitelist: true
        forbidNonWhitelisted: true
      
      error:
        obfuscateMessage: false
    application.conf.yaml: |-
      application:
        <<: *default-app
        name: bereal-moment-ui
        oauth: true
        session: true
        serviceContext: /main/moment/
        serviceUri: https://tools.dev.bereal.team/main/moment/
      
      hbs:
        fallbackIndex: index
        assets: ./assets
        views: ./
      
      session:
        redis: *redis-session
      
      oauth-client:
        <<: *default-oauth-client
        clientId: moment-ui
        clientSecret: 91E9DD7C-AFC8-44B6-8A20-74F6CE3BA823
        roles:
          - 'moments'
      
      bereal:
        moment-service:
          allowMultipleMoments: true
          allowCloseRegions: true
          # we can create a moment even if there is one "warming up" this is safe in dev env
          safeWarmupRangeInMinutes: 0
      
      pod:
        namespace: bereal-main
        domain: bereal
        feature: moment
        name: bereal-moment-ui
        type: ui
        registry: us-central1-docker.pkg.dev/shared-build-all/bereal
        command: domains/bereal/moment-ui/main.js
        version: "faafc67"
        image: bereal-monolith
        env: dev
        envRoot: dev
      build-info:
        app: bereal-moment-ui
        version: "faafc67"
        branch: main
        commit: "faafc67"
        env: dev
        envRoot: dev
      #  buildTime:
      
      ingress:
        enable: true
        suffix: main
