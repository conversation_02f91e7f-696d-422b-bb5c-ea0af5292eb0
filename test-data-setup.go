package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"cloud.google.com/go/civil"
	"cloud.google.com/go/spanner"
	"google.golang.org/api/option"
)

func main() {
	ctx := context.Background()

	// Connect to local Spanner emulator
	client, err := spanner.NewClient(ctx, "projects/backend-core-dev/instances/bereal-local-instance/databases/entity-user",
		option.WithEndpoint("localhost:9011"),
		option.WithoutAuthentication())
	if err != nil {
		log.Fatalf("Failed to create Spanner client: %v", err)
	}
	defer client.Close()

	// Create test data
	if err := createTestData(ctx, client); err != nil {
		log.Fatalf("Failed to create test data: %v", err)
	}

	fmt.Println("Test data created successfully!")
}

func createTestData(ctx context.Context, client *spanner.Client) error {
	// Test users with different streak scenarios
	testUsers := []struct {
		UserID      string
		Username    string
		Region      string
		PhoneNumber string
	}{
		{"user-perfect-streak", "perfect_user", "EU_WEST", "+33123456789"},
		{"user-with-gaps", "gappy_user", "EU_WEST", "+33123456790"},
		{"user-many-gaps", "many_gaps_user", "EU_WEST", "+33123456791"},
		{"user-no-streak", "no_streak_user", "EU_WEST", "+33123456792"},
		{"user-old-gaps", "old_gaps_user", "EU_WEST", "+33123456793"},
	}

	// Insert test users
	var mutations []*spanner.Mutation
	for _, user := range testUsers {
		mutations = append(mutations, spanner.InsertOrUpdate(
			"Users",
			[]string{"UserId", "Username", "PhoneNumber", "Region", "CreatedAt", "UpdatedAt", "CreatedBy", "UpdatedBy"},
			[]interface{}{user.UserID, user.Username, user.PhoneNumber, user.Region, spanner.CommitTimestamp, spanner.CommitTimestamp, "test-setup", "test-setup"},
		))
	}

	// Create test streaks
	now := time.Now()
	yesterday := now.AddDate(0, 0, -1)

	// Perfect streak user - has a current streak of 5
	mutations = append(mutations, spanner.InsertOrUpdate(
		"Streaks",
		[]string{"UserId", "Length", "LastPostCalendarDay", "LastPostMomentID", "UpdatedAt", "UpdatedBy"},
		[]interface{}{"user-perfect-streak", 5, civil.DateOf(yesterday), "moment-yesterday", spanner.CommitTimestamp, "test-setup"},
	))

	// User with gaps - has a streak of 2 but could be 5 with recovery
	mutations = append(mutations, spanner.InsertOrUpdate(
		"Streaks",
		[]string{"UserId", "Length", "LastPostCalendarDay", "LastPostMomentID", "UpdatedAt", "UpdatedBy"},
		[]interface{}{"user-with-gaps", 2, civil.DateOf(yesterday), "moment-yesterday", spanner.CommitTimestamp, "test-setup"},
	))

	// User with many gaps - has streak of 1 but has 15 gaps (not eligible)
	mutations = append(mutations, spanner.InsertOrUpdate(
		"Streaks",
		[]string{"UserId", "Length", "LastPostCalendarDay", "LastPostMomentID", "UpdatedAt", "UpdatedBy"},
		[]interface{}{"user-many-gaps", 1, civil.DateOf(yesterday), "moment-yesterday", spanner.CommitTimestamp, "test-setup"},
	))

	// User with no streak
	mutations = append(mutations, spanner.InsertOrUpdate(
		"Streaks",
		[]string{"UserId", "Length", "UpdatedAt", "UpdatedBy"},
		[]interface{}{"user-no-streak", 0, spanner.CommitTimestamp, "test-setup"},
	))

	// User with old gaps - has gaps but they're outside the 30-day window
	mutations = append(mutations, spanner.InsertOrUpdate(
		"Streaks",
		[]string{"UserId", "Length", "LastPostCalendarDay", "LastPostMomentID", "UpdatedAt", "UpdatedBy"},
		[]interface{}{"user-old-gaps", 3, civil.DateOf(yesterday), "moment-yesterday", spanner.CommitTimestamp, "test-setup"},
	))

	// Apply all mutations
	_, err := client.Apply(ctx, mutations)
	if err != nil {
		return fmt.Errorf("failed to insert test data: %w", err)
	}

	fmt.Println("✅ Created test users and streaks")
	return nil
}
