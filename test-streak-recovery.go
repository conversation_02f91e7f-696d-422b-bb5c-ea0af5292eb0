package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"

	pbUser "github.com/BeReal-App/backend-go/proto/entity/user/v1"
	pbCommon "github.com/BeReal-App/backend-go/proto/common/core/v1"
)

func main() {
	ctx := context.Background()

	// Connect to local user service
	conn, err := grpc.Dial("localhost:3005", grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		log.Fatalf("Failed to connect to user service: %v", err)
	}
	defer conn.Close()

	client := pbUser.NewUserServiceClient(conn)

	// Test scenarios
	testScenarios := []struct {
		name   string
		userID string
		expectedEligible bool
		expectedGaps int
	}{
		{
			name:   "Perfect Streak User (no gaps)",
			userID: "user-perfect-streak",
			expectedEligible: false, // No gaps to fill
			expectedGaps: 0,
		},
		{
			name:   "User with 2 gaps (eligible)",
			userID: "user-with-gaps",
			expectedEligible: true,
			expectedGaps: 2, // Days 2 and 3
		},
		{
			name:   "User with many gaps (not eligible)",
			userID: "user-many-gaps",
			expectedEligible: false, // Too many gaps (>10)
			expectedGaps: 24, // Many missing days
		},
		{
			name:   "User with no streak",
			userID: "user-no-streak",
			expectedEligible: false, // No current streak
			expectedGaps: 30, // All days missing
		},
		{
			name:   "User with old gaps (not eligible for old gaps)",
			userID: "user-old-gaps",
			expectedEligible: false, // Recent posts, no recent gaps
			expectedGaps: 25, // Missing days 5-29
		},
	}

	fmt.Println("🧪 Testing CalculateStreakRecovery function")
	fmt.Println("=" * 60)

	for _, scenario := range testScenarios {
		fmt.Printf("\n📋 Testing: %s\n", scenario.name)
		fmt.Printf("User ID: %s\n", scenario.userID)

		// Test CalculateStreakRecovery
		req := &pbUser.CalculateStreakRecoveryRequest{
			UserId:       scenario.userID,
			NumberOfDays: 30,
		}

		resp, err := client.CalculateStreakRecovery(ctx, req)
		if err != nil {
			fmt.Printf("❌ Error: %v\n", err)
			continue
		}

		calc := resp.Calculation
		fmt.Printf("Current Streak: %d\n", calc.CurrentStreak)
		fmt.Printf("Estimated Streak: %d\n", calc.EstimatedStreak)
		fmt.Printf("Gaps Found: %d\n", len(calc.GapsToFill))
		fmt.Printf("Is Eligible: %t\n", calc.IsEligible)

		if calc.ErrorMessage != "" {
			fmt.Printf("Error Message: %s\n", calc.ErrorMessage)
		}

		// Verify expectations
		if calc.IsEligible == scenario.expectedEligible {
			fmt.Printf("✅ Eligibility check passed\n")
		} else {
			fmt.Printf("❌ Eligibility check failed: expected %t, got %t\n", scenario.expectedEligible, calc.IsEligible)
		}

		// Test ApplyStreakRecovery if eligible and has gaps
		if calc.IsEligible && len(calc.GapsToFill) > 0 {
			fmt.Printf("\n🔧 Testing ApplyStreakRecovery...\n")
			
			applyReq := &pbUser.ApplyStreakRecoveryRequest{
				UserId:      scenario.userID,
				GapsToFill:  calc.GapsToFill,
			}

			applyResp, err := client.ApplyStreakRecovery(ctx, applyReq)
			if err != nil {
				fmt.Printf("❌ ApplyStreakRecovery Error: %v\n", err)
			} else {
				fmt.Printf("✅ ApplyStreakRecovery Success!\n")
				fmt.Printf("New Streak Length: %d\n", applyResp.NewStreak.Length)
				
				// Verify the streak was actually updated
				getStreakReq := &pbUser.GetStreakRequest{UserId: scenario.userID}
				getStreakResp, err := client.GetStreak(ctx, getStreakReq)
				if err != nil {
					fmt.Printf("❌ Error verifying updated streak: %v\n", err)
				} else {
					fmt.Printf("Verified Streak Length: %d\n", getStreakResp.Streak.Length)
				}
			}
		}

		fmt.Printf("─" * 40 + "\n")
	}

	fmt.Println("\n🎯 Testing edge cases...")
	
	// Test with invalid user ID
	fmt.Println("\n📋 Testing: Invalid User ID")
	invalidReq := &pbUser.CalculateStreakRecoveryRequest{
		UserId:       "non-existent-user",
		NumberOfDays: 30,
	}
	
	_, err = client.CalculateStreakRecovery(ctx, invalidReq)
	if err != nil {
		fmt.Printf("✅ Expected error for invalid user: %v\n", err)
	} else {
		fmt.Printf("❌ Expected error for invalid user but got success\n")
	}

	// Test with zero days
	fmt.Println("\n📋 Testing: Zero days parameter")
	zeroReq := &pbUser.CalculateStreakRecoveryRequest{
		UserId:       "user-perfect-streak",
		NumberOfDays: 0,
	}
	
	zeroResp, err := client.CalculateStreakRecovery(ctx, zeroReq)
	if err != nil {
		fmt.Printf("❌ Error with zero days: %v\n", err)
	} else {
		fmt.Printf("✅ Zero days handled (defaults to 30): gaps=%d\n", len(zeroResp.Calculation.GapsToFill))
	}

	fmt.Println("\n🎉 Testing completed!")
}
